/**
 * 视频详情API相关的工具函数和类型定义
 * 用于处理 /api/drill/video/detail/{id} 接口
 */

import { getRequest } from '@/services/api/api';

// 视频详情数据结构（基于API文档）
export interface DrillVideoDetail {
  id: number;                    // 主键ID
  taskId: number;               // 关联的钻孔任务ID
  title: string;                // 视频标题
  videoUrl: string;             // 视频URL
  startTime: string;            // 视频开始时间
  endTime: string;              // 视频结束时间
  stageStatus: number;          // 视频所属阶段 (0-下发任务 1-开孔申请 2-施工过程 3-退钻 4-封孔 5-终孔 6-连抽 7-废孔)
  createdAt: string;            // 创建时间
  updatedAt: string;            // 更新时间
  status: number;               // 状态 0-正常 1-删除
}

// API响应结构
export interface VideoDetailApiResponse {
  status: number;
  msg?: string;
  data?: DrillVideoDetail;
}

// 阶段状态映射
export const STAGE_STATUS_MAP: Record<number, string> = {
  0: '下发任务',
  1: '开孔申请', 
  2: '施工过程',
  3: '退钻',
  4: '封孔',
  5: '终孔',
  6: '连抽',
  7: '废孔'
};

/**
 * 获取视频详情
 * @param videoId 视频ID
 * @returns Promise<VideoDetailApiResponse>
 */
export const fetchVideoDetail = async (videoId: number): Promise<VideoDetailApiResponse> => {
  try {
    const response = await getRequest<VideoDetailApiResponse>(`/api/drill/video/detail/${videoId}`);
    
    // 记录API调用日志
    console.log('视频详情API调用:', {
      videoId,
      status: response.status,
      hasData: !!response.data,
      videoUrl: response.data?.videoUrl ? '已获取' : '未获取'
    });

    return response;
  } catch (error) {
    console.error('获取视频详情失败:', error);
    throw error;
  }
};

/**
 * 获取阶段状态描述
 * @param stageStatus 阶段状态码
 * @returns 阶段描述
 */
export const getStageStatusText = (stageStatus: number): string => {
  return STAGE_STATUS_MAP[stageStatus] || '未知阶段';
};

/**
 * 验证视频URL是否有效
 * @param videoUrl 视频URL
 * @returns boolean
 */
export const isValidVideoUrl = (videoUrl: string): boolean => {
  if (!videoUrl) return false;
  
  // 检查是否为ezopen协议
  if (videoUrl.startsWith('ezopen://')) {
    return true;
  }
  
  // 检查是否为其他有效的视频URL格式
  try {
    new URL(videoUrl);
    return true;
  } catch {
    return false;
  }
};
