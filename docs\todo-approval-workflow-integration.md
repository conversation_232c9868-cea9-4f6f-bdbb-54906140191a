# 待办详情审批流程对接

## 🎯 功能概述

根据真实的API数据，构建完整的审批流程显示，包括审批状态、审批人、审批时间等信息的真实对接。

## 📊 API审批相关字段分析

### 从API数据中提取的审批信息
```json
{
  "status": 0,  // 审批状态: 0:待审批 1:已同意 2：已拒绝
  "submitter": "祁强",  // 提交人
  "approver": "赵飞航",  // 实际审批人
  "approverAccount": "***********",  // 实际审批人账号
  "refuse": "拒绝",  // 拒绝原因
  "createdAt": "2025-04-14 16:51:32",  // 创建时间
  "approvalAt": "2025-07-08 16:07:32",  // 审批时间
  "ids": "[\"***********\", \"***********\", \"***********\"]"  // 可审批人员
}
```

### 审批状态映射
- **0**: 待审批 → `'pending'`
- **1**: 已同意 → `'approved'`
- **2**: 已拒绝 → `'rejected'`

## 🔧 技术实现

### 1. 审批流程构建逻辑

#### 三步审批流程
```typescript
approvalSteps: [
    // 第一步：提交申请（总是已完成）
    {
        id: 1,
        stepName: '提交申请',
        approver: apiData.submitter,  // 使用真实提交人
        status: 'approved',  // 提交步骤总是已完成
        isRead: true,
        approvalTime: apiData.createdAt,  // 使用创建时间
        comment: '提交申请',
        createTime: apiData.createdAt
    },
    
    // 第二步：审批处理（根据实际状态）
    {
        id: 2,
        stepName: '审批处理',
        approver: apiData.approver || '待分配',  // 使用真实审批人
        status: apiData.status === 0 ? 'pending' : 
                apiData.status === 1 ? 'approved' : 'rejected',
        isRead: apiData.status !== 0,  // 已处理则标记为已读
        approvalTime: apiData.approvalAt || undefined,  // 使用真实审批时间
        comment: apiData.status === 2 ? (apiData.refuse || '已拒绝') : 
                 apiData.status === 1 ? '审批通过' : '',
        signature: '',
        createTime: apiData.createdAt
    },
    
    // 第三步：流程完成（仅在审批通过时完成）
    {
        id: 3,
        stepName: '流程完成',
        approver: '',
        status: apiData.status === 1 ? 'approved' : 'pending',
        isRead: apiData.status === 1,
        approvalTime: apiData.status === 1 ? (apiData.approvalAt || undefined) : undefined,
        comment: apiData.status === 1 ? '流程已完成' : '',
        signature: '',
        createTime: apiData.createdAt
    }
]
```

### 2. 状态转换逻辑

#### API状态 → 组件状态
```typescript
// 整体状态转换
status: apiData.status === 0 ? 'pending' : 
        apiData.status === 1 ? 'approved' : 'rejected'

// 审批步骤状态转换
const getStepStatus = (apiStatus: number, stepId: number) => {
    switch (stepId) {
        case 1: // 提交步骤
            return 'approved';  // 总是已完成
        case 2: // 审批步骤
            return apiStatus === 0 ? 'pending' : 
                   apiStatus === 1 ? 'approved' : 'rejected';
        case 3: // 完成步骤
            return apiStatus === 1 ? 'approved' : 'pending';
        default:
            return 'pending';
    }
};
```

### 3. 审批评论逻辑

#### 根据状态生成评论
```typescript
const getApprovalComment = (apiStatus: number, refuse: string | null) => {
    switch (apiStatus) {
        case 0:
            return '';  // 待审批，无评论
        case 1:
            return '审批通过';  // 已同意
        case 2:
            return refuse || '已拒绝';  // 已拒绝，使用拒绝原因
        default:
            return '';
    }
};
```

### 4. 媒体文件处理

#### 图片和视频URL解析
```typescript
// 处理可能的逗号分隔的URL字符串
images: apiData.imageUrls ? 
    apiData.imageUrls.split(',').filter(url => url.trim()) : [],
videos: apiData.videoUrls ? 
    apiData.videoUrls.split(',').filter(url => url.trim()) : [],
```

## 📊 审批流程状态图

### 待审批状态 (status: 0)
```
✅ 提交申请 (祁强) - 2025-04-14 16:51:32
⏳ 审批处理 (赵飞航) - 待处理
⏸️ 流程完成 - 等待中
```

### 已同意状态 (status: 1)
```
✅ 提交申请 (祁强) - 2025-04-14 16:51:32
✅ 审批处理 (赵飞航) - 2025-07-08 16:07:32 "审批通过"
✅ 流程完成 - 2025-07-08 16:07:32 "流程已完成"
```

### 已拒绝状态 (status: 2)
```
✅ 提交申请 (祁强) - 2025-04-14 16:51:32
❌ 审批处理 (赵飞航) - 2025-07-08 16:07:32 "拒绝原因"
⏸️ 流程完成 - 等待中
```

## 🔄 数据流程

### 1. 审批数据处理流程
```
API返回审批数据
    ↓
解析审批状态 (0/1/2)
    ↓
确定审批人信息
    ↓
计算各步骤状态
    ↓
生成审批评论
    ↓
构建审批流程数组
    ↓
渲染审批流程UI
```

### 2. 状态变化处理
```
待审批 (status: 0)
    ↓ 审批操作
已同意 (status: 1) / 已拒绝 (status: 2)
    ↓ 页面刷新
重新获取最新状态
    ↓ 更新UI
显示最新审批结果
```

## 🎯 功能特点

### 1. 真实数据驱动
- ✅ **真实审批人**: 使用API返回的实际审批人信息
- ✅ **真实时间**: 使用API返回的创建时间和审批时间
- ✅ **真实状态**: 根据API状态动态计算各步骤状态
- ✅ **真实评论**: 根据审批结果生成相应的评论内容

### 2. 完整的状态覆盖
- ✅ **待审批**: 显示等待审批的状态
- ✅ **已同意**: 显示审批通过的完整流程
- ✅ **已拒绝**: 显示拒绝原因和相关信息
- ✅ **流程完成**: 仅在审批通过时标记为完成

### 3. 灵活的数据处理
- ✅ **空值处理**: 妥善处理null和undefined值
- ✅ **默认值**: 为缺失的数据提供合理的默认值
- ✅ **类型转换**: 确保数据类型的正确性

### 4. 用户体验优化
- ✅ **状态图标**: 不同状态使用不同的视觉标识
- ✅ **时间显示**: 清晰显示各个操作的时间点
- ✅ **评论信息**: 显示审批意见和拒绝原因

## 📊 数据映射表

### 审批基本信息
| API字段 | 组件字段 | 说明 |
|---------|----------|------|
| status | status | 0→pending, 1→approved, 2→rejected |
| submitter | approvalSteps[0].approver | 提交人 |
| approver | approvalSteps[1].approver | 审批人 |
| createdAt | approvalSteps[0].approvalTime | 提交时间 |
| approvalAt | approvalSteps[1].approvalTime | 审批时间 |
| refuse | approvalSteps[1].comment | 拒绝原因 |

### 媒体文件信息
| API字段 | 组件字段 | 处理方式 |
|---------|----------|----------|
| imageUrls | images | 逗号分隔字符串 → 数组 |
| videoUrls | videos | 逗号分隔字符串 → 数组 |

## 🧪 测试场景

### 1. 不同审批状态测试
- **待审批**: 检查第二步是否显示为pending
- **已同意**: 检查所有步骤是否显示为approved
- **已拒绝**: 检查第二步是否显示为rejected，第三步为pending

### 2. 数据完整性测试
- **有审批人**: 检查审批人信息是否正确显示
- **无审批人**: 检查是否显示"待分配"
- **有拒绝原因**: 检查拒绝原因是否正确显示
- **无拒绝原因**: 检查是否显示默认的"已拒绝"

### 3. 时间显示测试
- **有审批时间**: 检查时间是否正确显示
- **无审批时间**: 检查是否不显示时间

## 📁 相关文件

- `src/pages/Todo/components/TodoDetail.tsx` - 主要修改文件
- `docs/todo-approval-workflow-integration.md` - 本文档

## 🎉 总结

通过这次审批流程对接：

- 🎯 **真实审批数据**: 使用API返回的真实审批信息
- 🎯 **动态状态计算**: 根据API状态动态生成审批流程
- 🎯 **完整的状态覆盖**: 支持待审批、已同意、已拒绝三种状态
- 🎯 **用户友好显示**: 清晰的状态图标和时间信息

现在待办详情页面的审批流程完全基于真实的API数据，能够准确反映实际的审批状态和流程！🚀
