# 分机号管理模块

## 功能概述
分机号管理模块用于管理系统中的分机号配置，支持分机号的增删改查操作。

## API接口对接

### 1. 列表接口
- **接口地址**: `/extension/get_ls`
- **请求方法**: GET
- **请求参数**:
  - `page`: 页码（默认：1）
  - `perPage`: 每页数量（默认：10）
  - `account`: 账号模糊查询（可选）

### 2. 详情接口
- **接口地址**: `/extension/get_detail/{id}`
- **请求方法**: GET

### 3. 添加接口
- **接口地址**: `/extension/post_add`
- **请求方法**: POST
- **请求参数**:
  - `account`: 账号（必需）
  - `password`: 密码（必需）
  - `bindCode`: 关联code（可选）
  - `bindType`: 绑定类型，1-设备 2-用户（可选）
  - `status`: 状态，0-正常 1-禁用（可选，默认0）

### 4. 修改接口
- **接口地址**: `/extension/post_modify`
- **请求方法**: POST
- **请求参数**:
  - `id`: 分机号ID（必需）
  - `account`: 账号（必需）
  - `password`: 密码（必需）
  - `bindCode`: 关联code（可选）
  - `bindType`: 绑定类型（可选）
  - `status`: 状态（可选）

### 5. 删除接口
- **接口地址**: `/extension/post_delete`
- **请求方法**: POST
- **请求参数**:
  - `id`: 分机号ID（必需）

## 数据结构

### ExtensionItem（分机号数据项）
```typescript
interface ExtensionItem {
  id: number;                    // ID
  account: string;               // 账号
  password: string;              // 密码
  bindCode: string | null;       // 关联code
  bindType: number | null;       // 绑定类型：1-设备 2-用户
  status: number;                // 状态：0-正常 1-禁用
  corpId: string;                // 客户id
  createdAt: string;             // 创建时间
  updatedAt: string;             // 更新时间
}
```

### API响应格式
```typescript
interface ApiResponse<T> {
  status: number;                // 状态码
  msg: string;                   // 消息
  data: T;                       // 数据
}
```

## 页面路由
- 列表页面: `/system/extension`
- 添加页面: `/system/extension/add`
- 编辑页面: `/system/extension/edit?id={id}`

## 文件结构
```
src/pages/System/Extension/
├── index.tsx                  # 列表页面
├── add.tsx                    # 添加页面
├── edit.tsx                   # 编辑页面
├── mockService.ts             # API服务（实际使用真实API）
├── types.ts                   # 类型定义
├── components/
│   └── ExtensionForm.tsx      # 表单组件
└── README.md                  # 说明文档
```

## 使用说明
1. 在系统设置菜单中点击"分机号管理"进入列表页面
2. 支持按账号进行模糊搜索
3. 支持添加、编辑、删除分机号
4. 状态显示：正常（绿色）、禁用（红色）
5. 绑定类型显示：设备（蓝色）、用户（绿色）

## 注意事项
1. 账号必须唯一，不能重复
2. 密码在列表中以"******"形式显示，保护隐私
3. 删除操作需要确认，防止误删
4. 所有操作都有相应的成功/失败提示
