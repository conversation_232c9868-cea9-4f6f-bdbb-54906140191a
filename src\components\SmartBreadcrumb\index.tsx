import React, { useEffect, useState } from 'react';
import { history, useLocation } from 'umi';
import { CloseOutlined, ClearOutlined } from '@ant-design/icons';
import styles from './index.less';

interface BreadcrumbItem {
  title: string;
  path: string;
}

interface SmartBreadcrumbProps {
  maxHistory?: number; // 最大历史记录数量
}

// 路径到标题的映射
const pathToTitleMap: Record<string, string> = {
  '/home': '首页',
  '/home/<USER>': '任务看板',
  '/home/<USER>': '实时作业',
  '/todo': '待办',
  '/todo/detail': '待办详情',
  '/project': '施工管理',
  '/project/drill': '打孔管理',
  '/project/drill/add': '添加打孔计划',
  '/project/drill/detail': '打孔详情',
  '/project/construction': '施工记录',
  '/project/construction/modify': '施工记录详情',
  '/project/deliver': '下发任务',
  '/project/Deliver/modify': '下发计划详情',
  '/project/task/add': '添加下发计划',
  '/assetmana': '资产管理',
  '/assetmana/device': '设备管理',
  '/assetmana/device/detail': '设备详情',
  '/assetmana/device/detail/historRecord': '历史记录',
  '/assetmana/drilltool': '钻具管理',
  '/report': '报表',
  '/opermain': '运维中心',
  '/miningArea': '矿区概览',
  '/miningArea/miningFace': '采面管理',
  '/miningArea/Roadway': '巷道管理',
  '/miningArea/DrillField': '钻场管理',
  '/system': '系统设置',
  '/system/enterprise': '企业配置',
  '/system/character': '权限管理',
  '/system/character/add': '添加权限',
  '/system/character/detail': '权限详情',
  '/userinfo': '用户信息',
  '/newTaskDashboard': '新任务看板',
  '/auditLog': '审计日志',
  '/call': '呼叫',
};

const SmartBreadcrumb: React.FC<SmartBreadcrumbProps> = ({
  maxHistory = 10
}) => {
  const location = useLocation();
  const [visitHistory, setVisitHistory] = useState<BreadcrumbItem[]>([]);

  // 从localStorage加载历史记录
  useEffect(() => {
    const savedHistory = localStorage.getItem('breadcrumb_history');
    if (savedHistory) {
      try {
        setVisitHistory(JSON.parse(savedHistory));
      } catch (error) {
        console.error('Failed to parse breadcrumb history:', error);
      }
    }
  }, []);

  // 监听路由变化，更新历史记录
  useEffect(() => {
    const currentPath = location.pathname;
    const currentTitle = pathToTitleMap[currentPath];
    
    if (currentTitle) {
      const newItem: BreadcrumbItem = {
        title: currentTitle,
        path: currentPath
      };

      setVisitHistory(prev => {
        // 移除重复项
        const filtered = prev.filter(item => item.path !== currentPath);
        // 添加到开头
        const newHistory = [newItem, ...filtered];
        // 限制数量
        const limitedHistory = newHistory.slice(0, maxHistory);
        
        // 保存到localStorage
        localStorage.setItem('breadcrumb_history', JSON.stringify(limitedHistory));
        
        return limitedHistory;
      });
    }
  }, [location.pathname, maxHistory]);

  // 处理历史记录点击
  const handleHistoryClick = (path: string) => {
    if (path !== location.pathname) {
      history.push(path);
    }
  };

  // 处理删除历史记录项
  const handleRemoveHistoryItem = (pathToRemove: string, event: React.MouseEvent) => {
    event.stopPropagation(); // 阻止事件冒泡，避免触发点击跳转

    setVisitHistory(prev => {
      const newHistory = prev.filter(item => item.path !== pathToRemove);

      // 如果删除后没有历史记录了，清空localStorage
      if (newHistory.length === 0) {
        localStorage.removeItem('breadcrumb_history');
      } else {
        // 更新localStorage
        localStorage.setItem('breadcrumb_history', JSON.stringify(newHistory));
      }

      return newHistory;
    });
  };

  // 清空所有历史记录
  const handleClearAllHistory = (event: React.MouseEvent) => {
    event.stopPropagation();

    // 保留当前页面，只清空其他历史记录
    const currentPath = location.pathname;
    const currentTitle = pathToTitleMap[currentPath];

    if (currentTitle) {
      const currentItem: BreadcrumbItem = {
        title: currentTitle,
        path: currentPath
      };
      setVisitHistory([currentItem]);
      localStorage.setItem('breadcrumb_history', JSON.stringify([currentItem]));
    } else {
      setVisitHistory([]);
      localStorage.removeItem('breadcrumb_history');
    }
  };

  return (
    <div className={styles.smartBreadcrumb}>
      {/* 只保留访问历史快捷导航 */}
      {visitHistory.length > 0 && (
        <div className={styles.historyNav}>
          <span className={styles.historyLabel}>
            最近访问:
          </span>
          <div className={styles.historyContainer}>
            {visitHistory.map((item) => (
              <span
                key={item.path}
                className={`${styles.historyItem} ${item.path === location.pathname ? styles.active : ''}`}
                onClick={() => handleHistoryClick(item.path)}
              >
                <span className={styles.historyTitle}>{item.title}</span>
                <CloseOutlined
                  className={styles.closeIcon}
                  onClick={(e) => handleRemoveHistoryItem(item.path, e)}
                />
              </span>
            ))}
          </div>
          {visitHistory.length > 1 && (
            <ClearOutlined
              className={styles.clearAllIcon}
              title="清空历史记录"
              onClick={handleClearAllHistory}
            />
          )}
        </div>
      )}
    </div>
  );
};

export default SmartBreadcrumb;
