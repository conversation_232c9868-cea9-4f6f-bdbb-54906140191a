/**
 * 分机号管理相关类型定义
 */

// 分机号数据项接口
export interface ExtensionItem {
  id?: any;
  sipAccount: any;      // SIP账号
  sipPassword: any;     // SIP密码
  deviceCode: any;      // 设备编码
  status?: any;         // 状态：0-启用，1-禁用
  createTime?: any;     // 创建时间
  updateTime?: any;     // 更新时间
  remark?: any;         // 备注
}

// 表单数据接口
export interface ExtensionFormData {
  sipAccount: any;
  sipPassword: any;
  deviceCode: any;
  status?: any;
  remark?: any;
}

// API响应接口
export interface ApiResponse<T = any> {
  status: any;
  msg: any;
  data: T;
}

// 列表查询参数接口
export interface ExtensionListParams {
  page?: any;
  perPage?: any;
  sipAccount?: any;
  deviceCode?: any;
  status?: any;
}

// 列表响应数据接口
export interface ExtensionListResponse {
  items: any;
  total: any;
}
