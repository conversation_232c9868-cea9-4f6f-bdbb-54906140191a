# 班组效能数据对接说明

## 🎯 **对接目标**
将API返回的 `teamEfficiency` JSON字符串数据对接到班组效能表格组件中，实现真实数据的展示。

## 📊 **API数据格式**

### **原始数据结构**
```json
{
  "teamEfficiency": "{\"打钻一组\":{\"任务完成率\":\"50%\",\"平均孔深\":\"150\",\"已完成任务数\":1,\"异常次数\":0},\"打钻二组\":{\"任务完成率\":\"0%\",\"平均孔深\":\"0\",\"已完成任务数\":0,\"异常次数\":0}}"
}
```

### **解析后的数据结构**
```javascript
{
  "打钻一组": {
    "任务完成率": "50%",
    "平均孔深": "150", 
    "已完成任务数": 1,
    "异常次数": 0
  },
  "打钻二组": {
    "任务完成率": "0%",
    "平均孔深": "0",
    "已完成任务数": 0,
    "异常次数": 0
  }
}
```

## 🔧 **技术实现**

### **1. 数据类型定义**
```typescript
// API返回的班组效能数据结构
interface ApiTeamEfficiencyData {
  任务完成率: string;  // "50%"
  平均孔深: string;    // "150"
  已完成任务数: number; // 1
  异常次数: number;    // 0
}

// 表格显示的数据结构
interface TeamEfficiencyData {
  team: string;           // 班组名称
  taskComplete: string;   // 任务完成率 "50%"
  completedTasks: number; // 已完成任务数
  avgDistance: string;    // 平均孔深
  abnormalCount: number;  // 异常次数
}
```

### **2. 主页面数据处理**
```typescript
// 状态定义 - 存储JSON字符串
const [teamEfficiencyData, setTeamEfficiencyData] = useState<string>('');

// API数据处理 - 直接传递JSON字符串
const teamEfficiencyJson = data.teamEfficiency || '';
setTeamEfficiencyData(teamEfficiencyJson);
console.log('📊 班组效能JSON数据:', teamEfficiencyJson);
```

### **3. 组件数据解析**
```typescript
// 解析班组效能数据
const parseTeamEfficiencyData = (data: string | TeamEfficiencyData[] | undefined): TeamEfficiencyData[] => {
  if (!data) return [];
  
  // 如果已经是数组格式，直接返回
  if (Array.isArray(data)) return data;
  
  try {
    // 解析JSON字符串
    const parsedData: Record<string, ApiTeamEfficiencyData> = JSON.parse(data);
    console.log('🏢 解析班组效能数据:', parsedData);
    
    // 转换为表格数据格式
    return Object.entries(parsedData).map(([teamName, teamData]) => ({
      team: teamName,
      taskComplete: teamData.任务完成率,
      completedTasks: teamData.已完成任务数,
      avgDistance: teamData.平均孔深,
      abnormalCount: teamData.异常次数,
    }));
  } catch (error) {
    console.error('🏢 解析班组效能数据失败:', error);
    return [];
  }
};
```

## 🎨 **表格列配置**

### **列定义优化**
```typescript
const teamColumns = [
  {
    title: '班组名称',
    dataIndex: 'team',
    key: 'team',
    width: '20%',
  },
  {
    title: '任务完成率',
    dataIndex: 'taskComplete',
    key: 'taskComplete',
    width: '20%',
    render: (val: string) => val || '0%', // 直接显示字符串，已包含%符号
  },
  {
    title: '已完成任务数',
    dataIndex: 'completedTasks',
    key: 'completedTasks',
    width: '20%',
    render: (val: number) => val || 0,
  },
  {
    title: '平均孔深（m）',
    dataIndex: 'avgDistance',
    key: 'avgDistance',
    width: '20%',
    render: (val: string) => val || '0',
  },
  {
    title: '异常次数',
    dataIndex: 'abnormalCount',
    key: 'abnormalCount',
    width: '20%',
    render: (val: number) => val || 0,
  },
];
```

## 📋 **数据映射关系**

| API字段 | 中文含义 | 表格字段 | 数据类型 | 示例值 |
|---------|----------|----------|----------|--------|
| 班组名称 | 班组名称 | team | string | "打钻一组" |
| 任务完成率 | 任务完成率 | taskComplete | string | "50%" |
| 已完成任务数 | 已完成任务数 | completedTasks | number | 1 |
| 平均孔深 | 平均孔深 | avgDistance | string | "150" |
| 异常次数 | 异常次数 | abnormalCount | number | 0 |

## 🔍 **调试功能**

### **控制台输出**
```javascript
// 主页面调试信息
console.log('📊 班组效能JSON数据:', teamEfficiencyJson);

// 组件内调试信息
console.log('🏢 解析班组效能数据:', parsedData);
```

### **调试输出示例**
```
📊 班组效能JSON数据: {"打钻一组":{"任务完成率":"50%","平均孔深":"150","已完成任务数":1,"异常次数":0},"打钻二组":{"任务完成率":"0%","平均孔深":"0","已完成任务数":0,"异常次数":0}}

🏢 解析班组效能数据: {
  "打钻一组": {
    "任务完成率": "50%",
    "平均孔深": "150",
    "已完成任务数": 1,
    "异常次数": 0
  },
  "打钻二组": {
    "任务完成率": "0%", 
    "平均孔深": "0",
    "已完成任务数": 0,
    "异常次数": 0
  }
}
```

## ✅ **功能特点**

1. **灵活数据处理**: 支持JSON字符串和数组两种数据格式
2. **错误处理**: 完整的JSON解析错误处理机制
3. **数据验证**: 对空值和异常数据进行处理
4. **调试支持**: 详细的控制台调试信息
5. **类型安全**: 完整的TypeScript类型定义
6. **用户友好**: 空数据时显示友好的提示信息

## 🚀 **使用效果**

现在班组效能表格会显示：
- **打钻一组**: 任务完成率50%，已完成1个任务，平均孔深150m，异常次数0
- **打钻二组**: 任务完成率0%，已完成0个任务，平均孔深0m，异常次数0

数据完全来自API接口，实现了真实数据的动态展示！
