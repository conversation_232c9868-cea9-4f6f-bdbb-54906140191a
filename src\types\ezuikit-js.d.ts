declare module 'ezuikit-js' {
  export interface EZUIKitPlayerConfig {
    id: string;
    accessToken: string;
    url: string;
    width?: number;
    height?: number;
    template?: string;
    audio?: boolean;
    handleSuccess?: () => void;
    handleError?: (error: any) => void;
    staticPath?: string;
    themeData?: any;
    plugin?: string[];
    seekFrequency?: number;
    language?: string;
    debugDownloadData?: boolean;
    disableRenderPrivateData?: boolean;
    quality?: number | string;
    loggerOptions?: {
      name?: string;
      level?: 'INFO' | 'LOG' | 'WARN' | 'ERROR';
      showTime?: boolean;
    };
    streamInfoCBType?: 0 | 1;
    videoLevelList?: Array<{
      level: number;
      name: string;
      streamTypeIn: 1 | 2;
    }> | null;
    env?: {
      domain?: string;
    };
  }

  export interface EventEmitter {
    on(event: string, callback: (data?: any) => void): void;
    off(event: string, callback?: (data?: any) => void): void;
  }

  export class EZUIKitPlayer {
    eventEmitter: EventEmitter;
    
    constructor(config: EZUIKitPlayerConfig);
    
    // 播放控制方法
    play(): Promise<void>;
    stop(): Promise<void>;
    pause(): Promise<void>;
    resume(): Promise<void>;
    
    // 音频控制
    openSound(): Promise<void>;
    closeSound(): Promise<void>;
    setVolumeGain(volume: number): void;
    
    // 录制功能
    startSave(fileName?: string): Promise<void>;
    stopSave(): Promise<void>;
    
    // 截图功能
    capturePicture(fileName?: string, callback?: (data: any) => void): void;
    
    // 对讲功能
    startTalk(): Promise<void>;
    stopTalk(): Promise<void>;
    getMicrophonePermission(): Promise<{ code: number; [key: string]: any }>;
    getMicrophonesList(): Promise<{ code: number; [key: string]: any }>;
    setProfile(config: { microphoneId?: string }): void;
    
    // 全屏控制
    fullScreen(): void;
    cancelFullScreen(): void;
    
    // 其他功能
    getOSDTime(): Promise<string>;
    changePlayUrl(options: any): Promise<void>;
    enableZoom(): Promise<void>;
    closeZoom(): Promise<void>;
    resize(width: number, height: number): void;
    setFECCorrectType(config: { place: number; type: number }, canvasIds?: string): void;
    setLoggerOptions(options: {
      name?: string;
      level?: 'INFO' | 'LOG' | 'WARN' | 'ERROR';
      showTime?: boolean;
    }): void;
    
    // 主题控制
    Theme: {
      changeTheme(template: string): void;
    };
    
    // 静态属性
    static EVENTS: {
      // 流信息事件
      streamInfoCB: string;
      audioInfo: string;
      videoInfo: string;
      
      // 截图事件
      capturePicture: string;
      
      // 清晰度切换事件
      changeVideoLevel: string;
      
      // 音频事件
      openSound: string;
      closeSound: string;
      
      // 解码资源加载事件
      decoderLoad: string;
      decoderLoaded: string;
      
      // 销毁事件
      destroy: string;
      
      // 全屏事件
      fullscreen: string;
      exitFullscreen: string;
      fullscreenChange: string;
      
      // 播放事件
      firstFrameDisplay: string;
      init: string;
      resize: string;
      pause: string;
      play: string;
      resume: string;
      seek: string;
      stop: string;
      setPoster: string;
      setMirrorFlip: string;
      reSetTheme: string;
      recTimeChange: string;
      
      // HTTP接口事件
      http: {
        getCloudRecTimes: string;
        getCloudRecordTimes: string;
        getLocalRecTimes: string;
        getDeviceInfo: string;
        getDeviceList: string;
        setVideoLevel: string;
      };
      
      // 倍速事件
      fast: string;
      slow: string;
      speedChange: string;
      
      // 对讲事件
      startTalk: string;
      stopTalk: string;
      volumeChange: string;
      talkSuccess: string;
      talkError: string;
      
      // 录制事件
      startSave: string;
      stopSave: string;
      
      // 电子放大事件
      zoom: {
        openZoom: string;
        closeZoom: string;
        onZoomChange: string;
      };
      
      // 云台控制事件
      ptz: {
        openPtz: string;
        closePtz: string;
        ptzSpeedChange: string;
        ptzBtnClick: string;
        ptzDirection: string;
      };
      
      // 时间轴事件
      timeLine: {
        timeWidthChange: string;
      };
      
      // 日期选择器事件
      date: {
        openDatePanel: string;
        closeDatePanel: string;
        recStartTimeChange: string;
      };
    };
  }
}
