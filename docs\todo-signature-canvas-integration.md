# 待办详情电子签名画板功能集成文档

## 概述

本文档描述了在待办详情页面中集成电子签名画板功能的实现。该功能使用 `react-signature-canvas` 库，允许用户通过鼠标或触摸屏进行电子签名，替代了原有的上传签名图片功能。

## 功能特性

### 1. 画板签名
- 基于HTML5 Canvas的签名画板
- 支持鼠标和触摸屏操作
- 实时签名预览
- 签名数据转换为base64格式

### 2. 签名管理
- 清空签名功能
- 签名验证（检查是否为空）
- 自动保存签名数据
- 签名上传到服务器

### 3. 审批流程优化
- 移除审批意见输入框
- 点击"同意"直接弹出签名画板
- 点击"拒绝"直接执行拒绝操作
- 签名完成后自动确认审批

## 技术实现

### 1. 依赖库
```bash
# 安装签名画板库
pnpm add react-signature-canvas

# 安装类型定义
pnpm add -D @types/react-signature-canvas
```

### 2. 核心组件
```typescript
import SignatureCanvas from 'react-signature-canvas';

// 签名画板引用
const signatureRef = useRef<SignatureCanvas>(null);
```

### 3. 主要功能函数

#### 清空签名
```typescript
const clearSignature = () => {
    if (signatureRef.current) {
        signatureRef.current.clear();
    }
};
```

#### 签名确认
```typescript
const handleSignatureConfirm = async () => {
    if (!signatureRef.current) {
        message.error('签名画板未初始化');
        return;
    }

    if (signatureRef.current.isEmpty()) {
        message.error('请先进行签名');
        return;
    }

    try {
        // 获取签名的base64数据
        const signatureData = signatureRef.current.toDataURL();

        // 将base64签名转换为File对象
        const fileName = `signature_${Date.now()}.png`;
        const signatureFile = base64ToFile(signatureData, fileName);

        // 上传签名图片到服务器
        const uploadResponse = await uploadCaptureImage(signatureFile);

        // 更新审批状态
        if (currentStepId) {
            updateApprovalStatus(currentStepId, 'approved', signatureData);
        }

        message.success('签名确认成功');
    } catch (error) {
        message.error('签名上传失败，请重试');
    }
};
```

### 4. 画板配置
```typescript
<SignatureCanvas
    ref={signatureRef}
    canvasProps={{
        width: 500,
        height: 200,
        style: {
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            backgroundColor: 'white'
        }
    }}
    backgroundColor="white"
    penColor="black"
/>
```

## 用户界面

### 1. 弹窗设计
- 标题：电子签名
- 尺寸：600px宽度
- 自定义底部按钮：清空、取消、确认签名

### 2. 画板样式
- 尺寸：500x200像素
- 背景：白色
- 边框：浅灰色虚线
- 笔触：黑色

### 3. 用户提示
- 操作说明：可以用鼠标或触摸屏进行签名
- 错误提示：签名为空时的友好提示
- 成功反馈：签名完成后的确认消息

## 使用流程

### 审批同意流程
1. 用户点击"同意"按钮
2. 弹出电子签名画板
3. 用户在画板上进行签名
4. 点击"确认签名"按钮
5. 系统验证签名不为空
6. 获取签名base64数据
7. 将签名转换为File对象
8. 调用 `/api/file/upload_capture_image` 上传签名
9. 更新审批状态为已通过
10. 关闭签名弹窗

### 审批拒绝流程
1. 用户点击"拒绝"按钮
2. 直接更新审批状态为已拒绝
3. 无需签名操作

## API集成

### 签名上传接口
```typescript
// 使用抓拍图片上传接口
POST /api/file/upload_capture_image
Content-Type: multipart/form-data

参数:
- file: 签名图片文件 (从base64转换而来)
```

### 签名数据处理
```typescript
// 1. 获取签名base64数据
const signatureData = signatureRef.current.toDataURL();
// 格式: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."

// 2. 转换为File对象
const fileName = `signature_${Date.now()}.png`;
const signatureFile = base64ToFile(signatureData, fileName);

// 3. 上传到服务器
const uploadResponse = await uploadCaptureImage(signatureFile);
```

### 审批状态更新
```typescript
const updateApprovalStatus = (
    stepId: number,
    status: 'approved' | 'rejected',
    signature: string
) => {
    // 更新审批步骤状态
    // 保存签名数据到signature字段
    // 签名已上传到服务器，可保存服务器返回的URL或继续使用base64
};
```

## 优势特点

### 1. 用户体验
- 直观的签名操作
- 支持多种输入方式
- 实时预览效果
- 简化的操作流程

### 2. 技术优势
- 轻量级库，性能优秀
- 跨平台兼容性好
- 数据格式标准化
- 易于集成和维护

### 3. 安全性
- 签名数据本地生成
- base64格式便于传输
- 无需文件上传
- 减少安全风险

## 注意事项

1. **浏览器兼容性**: 确保目标浏览器支持HTML5 Canvas
2. **触摸设备**: 在移动设备上测试触摸签名功能
3. **签名质量**: 建议用户使用合适的输入设备
4. **数据大小**: base64签名数据相对较大，注意网络传输
5. **清晰度**: 可根据需要调整画板尺寸和分辨率

## 文件结构

```
src/
├── pages/Todo/components/
│   └── TodoDetail.tsx         # 待办详情组件（包含签名功能）
└── docs/
    └── todo-signature-canvas-integration.md  # 本文档
```

## 依赖项

- `react-signature-canvas`: 签名画板组件
- `@types/react-signature-canvas`: TypeScript类型定义
- `antd`: UI组件库
- `react`: 前端框架

## 测试建议

1. 测试不同设备的签名体验（鼠标、触摸屏、手写笔）
2. 测试签名数据的生成和保存
3. 测试清空功能的正确性
4. 测试签名验证逻辑
5. 测试在不同浏览器中的兼容性
