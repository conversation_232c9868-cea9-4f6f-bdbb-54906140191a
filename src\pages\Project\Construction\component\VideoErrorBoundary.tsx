import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON>, But<PERSON> } from 'antd';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

/**
 * 视频播放器错误边界组件
 * 专门用于捕获萤石云播放器的错误，防止整个页面崩溃
 */
class VideoErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // 更新 state 使下一次渲染能够显示降级后的 UI
    console.error('🚨 VideoErrorBoundary 捕获到错误:', error);
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 记录错误信息
    console.error('🚨 VideoErrorBoundary 详细错误信息:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack
    });

    // 可以将错误日志发送到错误报告服务
    // this.logErrorToService(error, errorInfo);
  }

  handleRetry = () => {
    // 重置错误状态，重新渲染子组件
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      // 自定义的错误 UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div style={{
          height: '400px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: '#000',
          borderRadius: '8px'
        }}>
          <div style={{ textAlign: 'center', maxWidth: '400px', padding: '20px' }}>
            <Alert
              message={<span style={{ color: '#000' }}>视频播放器出现错误</span>}
              description={
                <div>
                  <p style={{ color: '#000', marginBottom: '16px' }}>
                    视频播放器初始化失败，可能是以下原因：
                  </p>
                  <ul style={{ textAlign: 'left', color: '#000', marginBottom: '16px' }}>
                    <li>当前时间段没有视频数据</li>
                    <li>网络连接不稳定</li>
                    <li>设备访问权限问题</li>
                    <li>视频服务暂时不可用</li>
                  </ul>
                  <Button
                    type="primary"
                    onClick={this.handleRetry}
                    style={{ marginRight: '8px' }}
                  >
                    重新加载
                  </Button>
                  <Button
                    onClick={() => window.location.reload()}
                  >
                    刷新页面
                  </Button>
                </div>
              }
              type="warning"
              showIcon
              style={{
                background: 'rgba(255, 255, 255, 0.95)',
                border: '1px solid #d9d9d9'
              }}
            />

            {/* 开发环境下显示详细错误信息 */}
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details style={{
                marginTop: '16px',
                textAlign: 'left',
                background: '#f5f5f5',
                padding: '12px',
                borderRadius: '4px',
                fontSize: '12px'
              }}>
                <summary style={{ cursor: 'pointer', fontWeight: 'bold', color: '#000' }}>
                  开发调试信息 (点击展开)
                </summary>
                <pre style={{
                  marginTop: '8px',
                  whiteSpace: 'pre-wrap',
                  wordBreak: 'break-word',
                  color: '#000'
                }}>
                  {this.state.error.message}
                  {'\n\n'}
                  {this.state.error.stack}
                </pre>
              </details>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default VideoErrorBoundary;
