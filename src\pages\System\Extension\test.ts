/**
 * 分机号管理API测试文件
 * 用于测试API接口的对接是否正确
 *
 * 接口地址：
 * - 列表: POST /extension/get_ls
 * - 详情: POST /extension/get_info
 * - 添加: POST /extension/post_add
 * - 修改: POST /extension/post_modify
 * - 删除: POST /extension/post_del
 */

import { extensionService } from './mockService';
import { ExtensionFormData } from './types';

// 测试获取分机号列表
export const testGetList = async () => {
  try {
    console.log('=== 测试获取分机号列表 ===');
    const result = await extensionService.getList({
      page: 1,
      perPage: 10,
    });
    console.log('列表接口响应:', result);
    return result;
  } catch (error) {
    console.error('获取列表失败:', error);
    throw error;
  }
};

// 测试添加分机号
export const testAddExtension = async () => {
  try {
    console.log('=== 测试添加分机号 ===');
    const formData: ExtensionFormData = {
      account: 'test001',
      password: '123456',
      bindCode: 'DEV001',
      bindType: 1,
      status: 0,
    };
    const result = await extensionService.add(formData);
    console.log('添加接口响应:', result);
    return result;
  } catch (error) {
    console.error('添加分机号失败:', error);
    throw error;
  }
};

// 测试获取分机号详情
export const testGetDetail = async (id: number) => {
  try {
    console.log('=== 测试获取分机号详情 ===');
    const result = await extensionService.getDetail(id);
    console.log('详情接口响应:', result);
    return result;
  } catch (error) {
    console.error('获取详情失败:', error);
    throw error;
  }
};

// 测试修改分机号
export const testUpdateExtension = async (id: number) => {
  try {
    console.log('=== 测试修改分机号 ===');
    const formData: ExtensionFormData = {
      account: 'test001_updated',
      password: '*********',
      bindCode: 'DEV001_UPDATED',
      bindType: 2,
      status: 1,
    };
    const result = await extensionService.update(id, formData);
    console.log('修改接口响应:', result);
    return result;
  } catch (error) {
    console.error('修改分机号失败:', error);
    throw error;
  }
};

// 测试删除分机号
export const testDeleteExtension = async (id: number) => {
  try {
    console.log('=== 测试删除分机号 ===');
    const result = await extensionService.delete(id);
    console.log('删除接口响应:', result);
    return result;
  } catch (error) {
    console.error('删除分机号失败:', error);
    throw error;
  }
};

// 运行所有测试
export const runAllTests = async () => {
  try {
    console.log('开始运行分机号管理API测试...');
    
    // 1. 测试获取列表
    await testGetList();
    
    // 2. 测试添加（注意：这会实际添加数据）
    // const addResult = await testAddExtension();
    // const newId = addResult.data?.id;
    
    // 3. 测试获取详情（需要有效的ID）
    // if (newId) {
    //   await testGetDetail(newId);
    //   await testUpdateExtension(newId);
    //   await testDeleteExtension(newId);
    // }
    
    console.log('所有测试完成！');
  } catch (error) {
    console.error('测试过程中出现错误:', error);
  }
};

// 在浏览器控制台中可以调用的测试函数
if (typeof window !== 'undefined') {
  (window as any).extensionApiTest = {
    testGetList,
    testAddExtension,
    testGetDetail,
    testUpdateExtension,
    testDeleteExtension,
    runAllTests,
  };
  
  console.log('分机号管理API测试函数已挂载到 window.extensionApiTest');
  console.log('可用的测试方法:');
  console.log('- extensionApiTest.testGetList()');
  console.log('- extensionApiTest.testAddExtension()');
  console.log('- extensionApiTest.testGetDetail(id)');
  console.log('- extensionApiTest.testUpdateExtension(id)');
  console.log('- extensionApiTest.testDeleteExtension(id)');
  console.log('- extensionApiTest.runAllTests()');
}
