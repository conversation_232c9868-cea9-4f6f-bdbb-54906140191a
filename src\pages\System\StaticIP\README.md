# 静态IP管理模块

## 功能概述
静态IP管理模块用于管理系统中的静态IP地址配置，支持IP地址的增删改查操作。主要用于设备IP地址的统一管理和分配。

## API接口规范

### 1. 列表接口
- **接口地址**: `/staticip/get_ls`
- **请求方法**: POST
- **请求参数**:
  - `page`: 页码（默认：1）
  - `perPage`: 每页数量（默认：10）
  - `ipAddress`: IP地址模糊查询（可选）
  - `deviceCode`: 设备编码模糊查询（可选）

### 2. 详情接口
- **接口地址**: `/staticip/get_info`
- **请求方法**: GET
- **请求参数**:
  - `id`: 静态IP ID（必需）

### 3. 添加接口
- **接口地址**: `/staticip/post_add`
- **请求方法**: POST
- **请求参数**:
  - `ipAddress`: IP地址（必需）
  - `deviceCode`: 设备编码（必需）
  - `status`: 状态，0-正常 1-禁用（可选，默认0）

### 4. 修改接口
- **接口地址**: `/staticip/post_modify`
- **请求方法**: POST
- **请求参数**:
  - `id`: 静态IP ID（必需）
  - `ipAddress`: IP地址（必需）
  - `deviceCode`: 设备编码（必需）
  - `status`: 状态（可选）

### 5. 删除接口
- **接口地址**: `/staticip/post_del`
- **请求方法**: POST
- **请求参数**:
  - `id`: 静态IP ID（必需）

## 数据结构

### StaticIPItem（静态IP数据项）
```typescript
interface StaticIPItem {
  id: number;                    // ID
  ipAddress: string;             // IP地址
  deviceCode: string;            // 设备编码
  status: number;                // 状态：0-正常 1-禁用
  corpId: string;                // 客户id
  createdAt: string;             // 创建时间
  updatedAt: string;             // 更新时间
}
```

### API响应格式
```typescript
interface ApiResponse<T> {
  status: number;                // 状态码
  msg: string;                   // 消息
  data: T;                       // 数据
}
```

## 页面路由
- 列表页面: `/system/staticip`
- 添加页面: `/system/staticip/add`
- 编辑页面: `/system/staticip/edit?id={id}`

## 文件结构
```
src/pages/System/StaticIP/
├── index.tsx                  # 列表页面
├── add.tsx                    # 添加页面
├── edit.tsx                   # 编辑页面
├── service.ts                 # API服务
├── types.ts                   # 类型定义
├── components/
│   └── StaticIPForm.tsx       # 表单组件
└── README.md                  # 说明文档
```

## 功能特性
1. **IP地址验证**: 支持标准IPv4地址格式验证
2. **设备编码管理**: 支持设备编码的唯一性管理
3. **状态管理**: 支持正常/禁用状态切换
4. **搜索功能**: 支持按IP地址和设备编码进行模糊搜索
5. **复制功能**: IP地址和设备编码支持一键复制
6. **分页显示**: 支持分页查看和快速跳转

## 使用说明
1. 在系统设置菜单中点击"静态IP管理"进入列表页面
2. 支持按IP地址和设备编码进行模糊搜索
3. 支持添加、编辑、删除静态IP配置
4. 状态显示：正常（绿色）、禁用（红色）
5. IP地址和设备编码支持复制功能

## 注意事项
1. IP地址必须符合IPv4格式规范（如：*************）
2. 设备编码只能包含字母、数字、下划线和横线
3. 删除操作需要确认，防止误删
4. 所有操作都有相应的成功/失败提示
5. 建议IP地址和设备编码保持唯一性，避免冲突
