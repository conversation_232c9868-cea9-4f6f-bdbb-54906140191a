/**
 * 视频详情接口测试工具
 * 用于测试 /api/drill/video/detail/{id} 接口的功能
 */

import { getRequest } from '@/services/api/api';

// 视频详情数据结构
interface VideoDetailData {
  id: number;
  taskId: number;
  title: string;
  videoUrl: string;
  startTime: string;
  endTime: string;
  stageStatus: number;
  createdAt: string;
  updatedAt: string;
  status: number;
}

// API响应结构
interface VideoDetailApiResponse {
  status: number;
  msg?: string;
  data?: VideoDetailData;
}

// 阶段状态映射
const STAGE_STATUS_MAP: Record<number, string> = {
  0: '下发任务',
  1: '开孔申请', 
  2: '施工过程',
  3: '退钻',
  4: '封孔',
  5: '终孔',
  6: '连抽',
  7: '废孔'
};

/**
 * 测试视频详情接口
 * @param videoId 视频ID
 */
export const testVideoDetailApi = async (videoId: number) => {
  console.log('🧪 开始测试视频详情接口, videoId:', videoId);
  
  try {
    const response: VideoDetailApiResponse = await getRequest(`/api/drill/video/detail/${videoId}`);
    
    console.log('🧪 接口响应:', response);
    
    if (response.status === 0 && response.data) {
      const video = response.data;
      
      console.log('✅ 视频详情获取成功:');
      console.log('  - ID:', video.id);
      console.log('  - 标题:', video.title);
      console.log('  - 视频URL:', video.videoUrl);
      console.log('  - 开始时间:', video.startTime);
      console.log('  - 结束时间:', video.endTime);
      console.log('  - 阶段状态:', video.stageStatus, `(${STAGE_STATUS_MAP[video.stageStatus] || '未知'})`);
      console.log('  - 创建时间:', video.createdAt);
      console.log('  - 状态:', video.status === 0 ? '正常' : '删除');
      
      // 验证必要字段
      const validations = [
        { field: 'videoUrl', value: video.videoUrl, required: true },
        { field: 'title', value: video.title, required: true },
        { field: 'startTime', value: video.startTime, required: true },
        { field: 'endTime', value: video.endTime, required: true }
      ];
      
      const missingFields = validations.filter(v => v.required && !v.value);
      
      if (missingFields.length > 0) {
        console.warn('⚠️ 缺少必要字段:', missingFields.map(f => f.field));
      } else {
        console.log('✅ 所有必要字段都存在');
      }
      
      // 验证视频URL格式
      if (video.videoUrl) {
        if (video.videoUrl.startsWith('ezopen://')) {
          console.log('✅ 视频URL格式正确 (ezopen协议)');
        } else {
          console.warn('⚠️ 视频URL不是ezopen协议:', video.videoUrl);
        }
      }
      
      return video;
    } else {
      console.error('❌ 接口返回错误:', response.msg || '未知错误');
      return null;
    }
  } catch (error) {
    console.error('❌ 接口调用失败:', error);
    return null;
  }
};

/**
 * 批量测试多个视频ID
 * @param videoIds 视频ID数组
 */
export const testMultipleVideoDetails = async (videoIds: number[]) => {
  console.log('🧪 开始批量测试视频详情接口, videoIds:', videoIds);
  
  const results = [];
  
  for (const videoId of videoIds) {
    console.log(`\n--- 测试视频 ${videoId} ---`);
    const result = await testVideoDetailApi(videoId);
    results.push({ videoId, result });
    
    // 添加延迟避免请求过快
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log('\n🧪 批量测试完成，结果汇总:');
  results.forEach(({ videoId, result }) => {
    if (result) {
      console.log(`✅ 视频 ${videoId}: ${result.title} (${result.videoUrl ? 'URL正常' : 'URL缺失'})`);
    } else {
      console.log(`❌ 视频 ${videoId}: 获取失败`);
    }
  });
  
  return results;
};

/**
 * 在浏览器控制台中使用的快捷测试函数
 */
export const quickTest = {
  // 测试单个视频
  video: (id: number) => testVideoDetailApi(id),
  
  // 测试多个视频
  videos: (ids: number[]) => testMultipleVideoDetails(ids),
  
  // 测试常见的视频ID
  common: () => testMultipleVideoDetails([1, 2, 3, 4, 5])
};

// 在开发环境下将测试函数挂载到全局对象
if (process.env.NODE_ENV === 'development') {
  (window as any).videoDetailTest = quickTest;
  console.log('🧪 视频详情接口测试工具已加载，使用方法:');
  console.log('  - window.videoDetailTest.video(1) // 测试单个视频');
  console.log('  - window.videoDetailTest.videos([1,2,3]) // 测试多个视频');
  console.log('  - window.videoDetailTest.common() // 测试常见视频ID');
}
