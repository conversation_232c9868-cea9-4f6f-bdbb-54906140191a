import React from 'react';
import { ProForm, ProFormText, ProFormSelect, ProFormTextArea } from '@ant-design/pro-components';
import { Form, message } from 'antd';
import { ExtensionFormData } from '../types';

interface ExtensionFormProps {
  initialValues?: ExtensionFormData;
  onFinish: (values: ExtensionFormData) => Promise<void>;
  loading?: boolean;
}

const ExtensionForm: React.FC<ExtensionFormProps> = ({
  initialValues,
  onFinish,
  loading = false,
}) => {
  const [form] = Form.useForm();

  const handleFinish = async (values: ExtensionFormData) => {
    try {
      await onFinish(values);
    } catch (error) {
      message.error('操作失败，请重试');
    }
  };

  return (
    <ProForm
      form={form}
      layout="vertical"
      initialValues={initialValues}
      onFinish={handleFinish}
      submitter={{
        searchConfig: {
          submitText: '保存',
          resetText: '重置',
        },
        submitButtonProps: {
          loading,
        },
      }}
    >
      <ProFormText
        name="sipAccount"
        label="SIP账号"
        placeholder="请输入SIP账号"
        rules={[
          { required: true, message: '请输入SIP账号' },
          { pattern: /^\d{3,10}$/, message: 'SIP账号必须是3-10位数字' },
        ]}
        fieldProps={{
          maxLength: 10,
        }}
      />

      <ProFormText.Password
        name="sipPassword"
        label="SIP密码"
        placeholder="请输入SIP密码"
        rules={[
          { required: true, message: '请输入SIP密码' },
          { min: 6, message: 'SIP密码至少6位' },
          { max: 20, message: 'SIP密码最多20位' },
          {
            pattern: /^[a-zA-Z0-9!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]*$/,
            message: 'SIP密码只能包含字母、数字和特殊字符'
          },
        ]}
        fieldProps={{
          maxLength: 20,
        }}
      />

      <ProFormText
        name="deviceCode"
        label="设备编码"
        placeholder="请输入设备编码"
        rules={[
          { required: true, message: '请输入设备编码' },
          { min: 2, message: '设备编码至少2位' },
          { max: 50, message: '设备编码最多50位' },
        ]}
        fieldProps={{
          maxLength: 50,
        }}
      />

      <ProFormSelect
        name="status"
        label="状态"
        placeholder="请选择状态"
        options={[
          { label: '启用', value: 0 },
          { label: '禁用', value: 1 },
        ]}
        initialValue={0}
      />

      <ProFormTextArea
        name="remark"
        label="备注"
        placeholder="请输入备注信息"
        fieldProps={{
          maxLength: 200,
          rows: 4,
        }}
      />
    </ProForm>
  );
};

export default ExtensionForm;
