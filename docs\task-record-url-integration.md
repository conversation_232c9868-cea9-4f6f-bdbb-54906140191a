# 任务录像地址接口对接文档

## 接口概述

本文档描述了 `/api/ys/get_record_url` 接口的对接实现，该接口用于获取任务的录像地址。

## 接口规格

### 请求参数
- **URL**: `/api/ys/get_record_url`
- **方法**: POST
- **Content-Type**: `application/json`
- **Body参数**:
  ```json
  {
    "parentId": 123  // 任务ID (integer, 必需)
  }
  ```

### 响应数据结构

```json
{
  "status": 0,
  "msg": "request:ok",
  "data": "ezopen://open.ys7.com/33010175992677797274:33010042991117112440/1.rec?begin=20250403102918&end=20250702142409"
}
```

- `status`: 响应状态码 (0表示成功)
- `msg`: 响应消息
- `data`: 视频URL字符串 (ezopen协议格式)

## 实现方案

### 1. 数据流程

1. **页面加载**: 施工记录详情页面通过URL参数获取任务ID
2. **传递参数**: 任务ID作为 `number` 参数传递给VideoPlayer组件
3. **获取录像**: VideoPlayer组件使用任务ID调用 `/api/ys/get_record_url` 接口
4. **播放视频**: 获取到的ezopen URL直接用于萤石云播放器

### 2. 关键代码实现

#### 接口调用函数
```typescript
const fetchTaskRecordUrl = async () => {
  try {
    const response: TaskRecordUrlResponse = await postRequest('/api/ys/get_record_url', {
      parentId: number // 使用任务ID作为parentId
    });

    if (response.status === 0 && response.data) {
      const recordUrl = response.data;
      setTaskRecordUrl(recordUrl);
      setEzOpenUrl(recordUrl); // 设置为默认播放地址
    }
  } catch (error) {
    messageApi.error('获取任务录像地址失败，请稍后重试');
  }
};
```

#### 页面初始化
```typescript
useEffect(() => {
  if (number) {
    fetchTaskRecordUrl(); // 获取任务录像地址
    fetchVideoList();      // 获取视频列表
  }
}, [number]);
```

### 3. 用户体验设计

#### 播放优先级
1. **任务录像**: 页面加载时自动获取并播放任务录像
2. **视频片段**: 用户点击视频列表时播放对应片段
3. **无视频**: 显示"请选择视频进行播放"提示

#### 状态显示
- **任务录像播放时**: 显示"当前播放: 任务录像"
- **视频片段播放时**: 显示"当前播放时间: [具体时间]"
- **无播放内容时**: 不显示播放状态

### 4. 错误处理

- **网络错误**: 显示友好的错误提示
- **接口返回错误**: 显示服务器返回的错误信息
- **URL无效**: 播放器会显示"暂无视频"

## 使用场景

### 场景1: 正常流程
1. 用户进入施工记录详情页面
2. 切换到"视频回放"标签页
3. 系统自动获取任务录像地址并开始播放
4. 用户可以点击视频列表查看特定时间段的录像

### 场景2: 无录像数据
1. 用户进入页面
2. 接口返回空数据或错误
3. 显示提示信息，用户可以查看视频列表中的其他片段

### 场景3: 网络异常
1. 接口调用失败
2. 显示错误提示
3. 用户可以刷新页面重试

## 技术细节

### 参数传递链路
```
施工记录详情页面 (URL: ?id=123)
  ↓ 获取任务信息 (drill/get_info)
  ↓ 传递任务ID
VideoPlayer组件 (number=123)
  ↓ 调用录像接口
/api/ys/get_record_url (parentId=123)
  ↓ 返回视频URL
萤石云播放器 (ezOpenUrl)
```

### 状态管理
- `taskRecordUrl`: 存储任务录像地址
- `ezOpenUrl`: 当前播放的视频地址
- `selectedVideo`: 当前选中的视频片段ID

## 测试建议

1. **正常场景测试**
   - 有效任务ID的录像获取
   - 视频播放功能验证

2. **异常场景测试**
   - 无效任务ID
   - 网络错误
   - 服务器错误响应

3. **交互测试**
   - 任务录像与视频片段切换
   - 页面刷新后的状态保持

## 相关文件

- `src/pages/Project/Construction/component/videoPlayer.tsx` - 主要实现文件
- `src/pages/Project/Construction/modify.tsx` - 施工记录详情页面
- `src/services/api/api.ts` - API请求工具函数
