import React, { useState } from 'react';
import { Card, Button, Input, Space, message } from 'antd';
import EzVideoPlayer from './EzVideoPlayer';

const EzVideoTest: React.FC = () => {
  const [ezOpenUrl, setEzOpenUrl] = useState<string>('');
  const [isPlaying, setIsPlaying] = useState<boolean>(false);

  // 测试用的 ezopen URL (写死的测试地址)
  const testUrl = 'ezopen://open.ys7.com/33010175992677797274:33010042991117112440/1.rec?begin=20250701000000&end=20250702235959';

  const handlePlay = () => {
    if (!ezOpenUrl.trim()) {
      message.warning('请输入有效的 ezopen:// 协议地址');
      return;
    }

    if (!ezOpenUrl.startsWith('ezopen://')) {
      message.error('请输入正确的 ezopen:// 协议地址');
      return;
    }

    setIsPlaying(true);
  };

  const handleStop = () => {
    setIsPlaying(false);
    // 重新设置URL来触发播放器重新初始化
    const currentUrl = ezOpenUrl;
    setEzOpenUrl('');
    setTimeout(() => {
      setEzOpenUrl(currentUrl);
    }, 100);
  };

  const handleUseTestUrl = () => {
    setEzOpenUrl(testUrl);
    message.info('已设置测试 URL');
  };

  const handlePlayTestUrl = () => {
    setEzOpenUrl(testUrl);
    setIsPlaying(true);
    message.info('开始播放测试 URL');
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card title="萤石云 ezopen:// 协议测试" style={{ marginBottom: '24px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <label style={{ display: 'block', marginBottom: '8px' }}>
              ezopen:// 协议地址:
            </label>
            <Input.TextArea
              value={ezOpenUrl}
              onChange={(e) => setEzOpenUrl(e.target.value)}
              placeholder="请输入 ezopen:// 协议地址，例如: ezopen://open.ys7.com/设备序列号/通道号.rec?begin=开始时间&end=结束时间"
              rows={3}
              style={{ marginBottom: '12px' }}
            />
          </div>

          <Space>
            <Button type="primary" onClick={handlePlay} disabled={!ezOpenUrl.trim()}>
              播放视频
            </Button>
            <Button onClick={handleStop} disabled={!isPlaying}>
              停止播放
            </Button>
            <Button onClick={handleUseTestUrl}>
              使用测试 URL
            </Button>
            <Button type="primary" ghost onClick={handlePlayTestUrl}>
              直接播放测试 URL
            </Button>
          </Space>

          <div style={{ fontSize: '12px', color: '#666' }}>
            <p><strong>说明:</strong></p>
            <ul>
              <li>ezopen:// 是萤石云的私有协议</li>
              <li>需要有效的萤石云 AccessToken 才能播放</li>
              <li>AccessToken 通过 /api/ys/get_access_token 接口获取</li>
              <li>确保设备在线且有访问权限</li>
            </ul>
          </div>
        </Space>
      </Card>

      {/* 视频播放器 */}
      {isPlaying && ezOpenUrl && (
        <Card title="视频播放" style={{ marginTop: '24px' }}>
          <div style={{ width: '100%', height: '500px' }}>
            <EzVideoPlayer
              ezOpenUrl={ezOpenUrl}
              width="100%"
              height="100%"
              style={{
                border: '1px solid #d9d9d9',
                borderRadius: '8px'
              }}
            />
          </div>
        </Card>
      )}
    </div>
  );
};

export default EzVideoTest;
