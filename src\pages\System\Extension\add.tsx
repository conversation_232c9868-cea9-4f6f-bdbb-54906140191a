import React, { useState } from 'react';
import { Card, message } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { history } from '@umijs/max';
import { mockExtensionService } from './mockService';
import ExtensionForm from './components/ExtensionForm';
import { ExtensionFormData } from './types';

const AddExtension: React.FC = () => {
  const [loading, setLoading] = useState(false);

  // 处理表单提交
  const handleFinish = async (values: ExtensionFormData) => {
    setLoading(true);
    try {
      const result = await mockExtensionService.add(values);
      if (result.status === 0) {
        message.success('添加分机号成功');
        history.push('/system/extension');
      } else {
        message.error(result.msg || '添加失败');
      }
    } catch (error) {
      message.error('添加失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 返回列表页
  const handleBack = () => {
    history.push('/system/extension');
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <ArrowLeftOutlined
              style={{ cursor: 'pointer' }}
              onClick={handleBack}
            />
            <span>添加分机号</span>
          </div>
        }
        bordered={false}
      >
        <div style={{ maxWidth: '600px' }}>
          <ExtensionForm
            onFinish={handleFinish}
            loading={loading}
          />
        </div>
      </Card>
    </div>
  );
};

export default AddExtension;
