/*
 * @Author: AI Assistant
 * @Date: 2025-01-02
 * @Description: 待办事项管理页面
 */

import React from 'react';
import { PageContainer } from '@ant-design/pro-components';
import TodoList from '../Construction/component/todoList';

const TodoManagement: React.FC = () => {
    return (
        <PageContainer
            header={{
                title: '待办事项管理',
                breadcrumb: {
                    items: [
                        {
                            path: '/',
                            title: '首页',
                        },
                        {
                            path: '/project',
                            title: '项目管理',
                        },
                        {
                            title: '待办事项',
                        },
                    ],
                },
            }}
        >
            <TodoList />
        </PageContainer>
    );
};

export default TodoManagement;
