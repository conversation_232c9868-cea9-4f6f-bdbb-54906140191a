# 萤石播放器直播模板优化 - 隐藏日期选择器

## 🎯 问题描述

在实时作业监控页面中，萤石播放器右下角显示了日期选择器，但对于直播场景来说这个功能是不必要的，需要将其隐藏。

## 🔍 问题分析

萤石云播放器（EZUIKitPlayer）提供了不同的模板：
- **`pcRec`** - 录像回放模板，包含日期选择器、时间轴等回放控件
- **`pcLive`** - 直播模板，只包含基本的播放控件，不显示日期选择器

原来的代码统一使用了 `pcRec` 模板，导致直播场景也显示了不必要的日期选择器。

## 🔧 解决方案

### 1. 扩展EzVideoPlayer组件属性

为EzVideoPlayer组件添加 `isLive` 属性来区分直播和录像模式：

```typescript
interface EzVideoPlayerProps {
  ezOpenUrl: string;
  width?: number | string;
  height?: number | string;
  className?: string;
  style?: React.CSSProperties;
  onCapture?: (type: 'image' | 'video', data: any) => void;
  isLive?: boolean; // 新增：是否为直播模式
}
```

### 2. 动态选择播放器模板

根据 `isLive` 属性动态选择合适的模板：

```typescript
const EzVideoPlayer: React.FC<EzVideoPlayerProps> = ({
  ezOpenUrl,
  width = '100%',
  height = '100%',
  className,
  style,
  onCapture,
  isLive = false // 默认为录像模式
}) => {
  // ... 其他代码

  player = new EZUIKitPlayer({
    id: containerId,
    accessToken: token,
    url: url,
    template: isLive ? 'pcLive' : 'pcRec', // 动态选择模板
    audio: true,
    // ... 其他配置
  });
}
```

### 3. 在Monitor组件中启用直播模式

在实时作业监控页面中，为EzVideoPlayer设置 `isLive={true}`：

```typescript
<EzVideoPlayer
  key={ezOpenUrl}
  ezOpenUrl={ezOpenUrl}
  width="100%"
  height="580px"
  style={{
    borderRadius: '8px',
    overflow: 'hidden'
  }}
  onCapture={handleEzCapture}
  isLive={true} // 启用直播模式，隐藏日期选择器
/>
```

## 📊 模板对比

### pcRec 模板（录像回放）
```
┌─────────────────────────────────────┐
│           视频播放区域                │
│                                     │
│                                     │
└─────────────────────────────────────┘
┌─────────────────────────────────────┐
│  ⏮ ⏸ ⏭   时间轴   📅日期选择器      │
└─────────────────────────────────────┘
```

**特点**:
- ✅ 包含完整的回放控件
- ✅ 时间轴可以拖拽跳转
- ✅ 日期选择器可以选择回放日期
- ❌ 对直播场景来说功能冗余

### pcLive 模板（直播）
```
┌─────────────────────────────────────┐
│           视频播放区域                │
│                                     │
│                                     │
└─────────────────────────────────────┘
┌─────────────────────────────────────┐
│  ⏸ 🔊 📷 📹                        │
└─────────────────────────────────────┘
```

**特点**:
- ✅ 简洁的直播控件
- ✅ 基本的播放/暂停功能
- ✅ 音量控制
- ✅ 抓拍功能
- ✅ 无日期选择器
- ✅ 适合实时直播场景

## 🎯 使用场景

### 直播场景 (isLive=true)
```typescript
<EzVideoPlayer
  ezOpenUrl="ezopen://..."
  isLive={true}  // 使用pcLive模板
/>
```

**适用于**:
- ✅ 实时作业监控
- ✅ 设备实时监控
- ✅ 现场直播

### 录像回放场景 (isLive=false)
```typescript
<EzVideoPlayer
  ezOpenUrl="ezopen://..."
  isLive={false}  // 使用pcRec模板（默认）
/>
```

**适用于**:
- ✅ 施工记录回放
- ✅ 历史视频查看
- ✅ 事件回放分析

## 🔄 向后兼容性

### 默认行为保持不变
```typescript
// 不传isLive属性时，默认为false，使用pcRec模板
<EzVideoPlayer ezOpenUrl="ezopen://..." />
// 等同于
<EzVideoPlayer ezOpenUrl="ezopen://..." isLive={false} />
```

### 现有代码无需修改
所有现有的EzVideoPlayer使用都会继续正常工作，因为：
- `isLive` 属性是可选的
- 默认值为 `false`，保持原有的 `pcRec` 模板行为

## 📁 修改的文件

### 1. EzVideoPlayer.tsx
```typescript
// 新增isLive属性
interface EzVideoPlayerProps {
  // ... 其他属性
  isLive?: boolean;
}

// 动态选择模板
template: isLive ? 'pcLive' : 'pcRec'
```

### 2. monitor.tsx
```typescript
// 在直播场景中启用直播模式
<EzVideoPlayer
  // ... 其他属性
  isLive={true}
/>
```

## 🧪 测试验证

### 1. 直播模式测试
- 进入实时作业监控页面
- 选择设备并切换到"监控"标签
- 验证播放器右下角**没有**日期选择器
- 验证基本的播放控件正常工作

### 2. 录像模式测试
- 进入施工记录详情页面
- 查看视频回放功能
- 验证播放器右下角**有**日期选择器
- 验证时间轴和回放功能正常工作

### 3. 兼容性测试
- 检查所有使用EzVideoPlayer的页面
- 确认没有传isLive属性的组件仍然正常工作
- 确认默认行为与之前一致

## 🎉 预期效果

### 直播场景 (实时作业监控)
- ✅ **简洁的界面**: 移除了不必要的日期选择器
- ✅ **更好的用户体验**: 界面更专注于直播内容
- ✅ **功能精准**: 只显示直播相关的控件

### 录像场景 (施工记录回放)
- ✅ **完整的回放功能**: 保留所有回放控件
- ✅ **向后兼容**: 现有功能完全不受影响
- ✅ **用户习惯**: 保持用户熟悉的操作方式

## 🔍 技术细节

### 萤石云模板说明
```typescript
// 萤石云播放器支持的主要模板
const templates = {
  'pcLive': '直播模板 - 简洁的直播控件',
  'pcRec': '回放模板 - 完整的回放控件',
  'mobileLive': '移动端直播模板',
  'mobileRec': '移动端回放模板'
};
```

### 模板选择逻辑
```typescript
// 根据使用场景选择合适的模板
const getTemplate = (isLive: boolean, isMobile: boolean) => {
  if (isMobile) {
    return isLive ? 'mobileLive' : 'mobileRec';
  } else {
    return isLive ? 'pcLive' : 'pcRec';
  }
};
```

## 📋 总结

通过这次优化：

1. **🎯 解决了问题**: 直播场景不再显示不必要的日期选择器
2. **🔧 提升了体验**: 界面更简洁，更专注于直播内容
3. **🛡️ 保持兼容**: 现有功能完全不受影响
4. **🚀 扩展了能力**: 为不同场景提供了合适的播放器模板

现在实时作业监控的萤石播放器将使用专门的直播模板，提供更好的用户体验！🎉
