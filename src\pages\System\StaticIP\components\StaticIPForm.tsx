import React from 'react';
import { ProForm, ProFormText, ProFormSelect } from '@ant-design/pro-components';
import { Form, message } from 'antd';
import { StaticIPFormData } from '../types';

interface StaticIPFormProps {
  initialValues?: StaticIPFormData;
  onFinish: (values: StaticIPFormData) => Promise<void>;
  loading?: boolean;
}

const StaticIPForm: React.FC<StaticIPFormProps> = ({
  initialValues,
  onFinish,
  loading = false,
}) => {
  const [form] = Form.useForm();

  const handleFinish = async (values: StaticIPFormData) => {
    try {
      await onFinish(values);
    } catch (error) {
      message.error('操作失败，请重试');
    }
  };

  // IP地址验证规则
  const validateIP = (_: any, value: string) => {
    if (!value) {
      return Promise.reject(new Error('请输入IP地址'));
    }
    
    const ipRegex = /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    if (!ipRegex.test(value)) {
      return Promise.reject(new Error('请输入有效的IP地址'));
    }
    
    return Promise.resolve();
  };

  return (
    <ProForm
      form={form}
      layout="vertical"
      initialValues={initialValues}
      onFinish={handleFinish}
      submitter={{
        searchConfig: {
          submitText: '保存',
          resetText: '重置',
        },
        submitButtonProps: {
          loading,
        },
      }}
    >
      <ProFormText
        name="ipAddress"
        label="IP地址"
        placeholder="请输入IP地址，如：*************"
        rules={[
          { validator: validateIP },
        ]}
        fieldProps={{
          maxLength: 15,
        }}
      />

      <ProFormText
        name="deviceCode"
        label="设备编码"
        placeholder="请输入设备编码"
        rules={[
          { required: true, message: '请输入设备编码' },
          { min: 2, message: '设备编码至少2位' },
          { max: 50, message: '设备编码最多50位' },
          {
            pattern: /^[a-zA-Z0-9_-]+$/,
            message: '设备编码只能包含字母、数字、下划线和横线'
          },
        ]}
        fieldProps={{
          maxLength: 50,
        }}
      />

      <ProFormSelect
        name="status"
        label="状态"
        placeholder="请选择状态"
        options={[
          { label: '正常', value: 0 },
          { label: '禁用', value: 1 },
        ]}
        initialValue={0}
      />
    </ProForm>
  );
};

export default StaticIPForm;
