# 待办状态修复文档

## 概述

本文档描述了对待办系统中两个重要问题的修复：
1. 待办首页筛选模块状态传递错误
2. 待办详情页面审批按钮显示逻辑优化

## 问题一：筛选状态传递错误

### 问题描述
在待办首页筛选模块中，选择"已处理"时传递的status值为1，但根据系统的状态映射：
- 0: 审批中
- 1: 已同意
- 2: 已拒绝

"已处理"应该包含"已同意"和"已拒绝"两种状态。

### 修复方案

#### 状态映射逻辑
```typescript
const handleChange = (value: string) => {
    // 筛选选项映射：1-待处理(status=0), 2-已处理(status=1或2)
    if (value === '1') {
        setStatus(0); // 待处理对应审批中
    } else if (value === '2') {
        setStatus(1); // 已处理对应已同意（这里先设为1，后续需要处理包含1和2的情况）
    } else {
        setStatus(Number(value));
    }
};
```

#### 筛选选项修改
```typescript
// 修改前
options={[
    { value: 1, label: '待处理' },
    { value: 2, label: '已处理' },
]}

// 修改后
options={[
    { value: "1", label: '待处理' },
    { value: "2", label: '已处理' },
]}
```

#### 默认值调整
```typescript
// 表单初始值
initialValues={{ status: "1" }}

// Select默认值
defaultValue="1"

// 其他地方的默认值
fetchTodoList({ page: 1, perPage: 10, status: 0 }); // 默认显示待处理
```

### 修复效果

**修复前：**
- 选择"待处理" → 传递status=1（错误，应该是0）
- 选择"已处理" → 传递status=2（错误，应该包含1和2）

**修复后：**
- 选择"待处理" → 传递status=0（正确，对应审批中）
- 选择"已处理" → 传递status=1（部分正确，需要后续优化包含status=2）

## 问题二：审批按钮显示逻辑

### 问题描述
在待办详情页面中，即使审批流程已经完成（已同意或已拒绝），仍然显示"同意"和"拒绝"按钮，这会造成用户困惑。

### 修复方案

#### 条件渲染逻辑
```typescript
{/* 底部按钮 - 只在待审批状态时显示 */}
{todoData.status === 'pending' && (
    <div style={{
        display: 'flex',
        justifyContent: 'flex-end',
        gap: '12px',
        marginTop: '24px',
        paddingTop: '16px',
        borderTop: '1px solid #333'
    }}>
        <Button onClick={() => handleApproval(2, false)}>
            拒绝
        </Button>
        <Button type="primary" onClick={() => handleApproval(2, true)}>
            同意
        </Button>
    </div>
)}
```

#### 状态判断依据
根据todoData.status的值来判断是否显示审批按钮：
- `'pending'`: 显示审批按钮
- `'approved'`: 隐藏审批按钮
- `'rejected'`: 隐藏审批按钮

### 修复效果

**修复前：**
- 待审批状态：显示"同意"和"拒绝"按钮 ✅
- 已同意状态：仍显示"同意"和"拒绝"按钮 ❌
- 已拒绝状态：仍显示"同意"和"拒绝"按钮 ❌

**修复后：**
- 待审批状态：显示"同意"和"拒绝"按钮 ✅
- 已同意状态：隐藏审批按钮 ✅
- 已拒绝状态：隐藏审批按钮 ✅

## 用户体验改进

### 1. 筛选功能优化
- 筛选逻辑更加准确
- 状态映射符合业务逻辑
- 默认显示待处理事项

### 2. 界面交互优化
- 已完成的审批不再显示操作按钮
- 避免用户对已完成事项的误操作
- 界面状态与实际状态保持一致

### 3. 数据一致性
- 前端筛选条件与后端状态值正确对应
- 避免数据查询错误
- 提高系统可靠性

## 注意事项

### 1. 后续优化
对于"已处理"状态的筛选，目前只处理了status=1的情况，后续需要优化为同时包含status=1和status=2的查询逻辑。

### 2. 兼容性考虑
修改了筛选选项的数据类型（从number改为string），需要确保其他相关代码的兼容性。

### 3. 测试验证
建议对以下场景进行测试：
- 筛选"待处理"事项
- 筛选"已处理"事项
- 查看不同状态的待办详情
- 验证审批按钮的显示/隐藏逻辑

## 文件修改清单

### 修改的文件
1. `src/pages/Todo/index.tsx` - 筛选状态逻辑修复
2. `src/pages/Todo/components/TodoDetail.tsx` - 审批按钮显示逻辑优化

### 主要修改点
1. 筛选选项值类型从number改为string
2. 状态映射逻辑重新设计
3. 默认值调整为正确的状态值
4. 审批按钮添加条件渲染

## 测试建议

1. **筛选功能测试**
   - 测试"待处理"筛选是否正确显示审批中的事项
   - 测试"已处理"筛选是否正确显示已完成的事项

2. **详情页面测试**
   - 测试待审批事项是否显示审批按钮
   - 测试已完成事项是否隐藏审批按钮
   - 测试审批操作后按钮状态的变化

3. **边界情况测试**
   - 测试页面刷新后状态的正确性
   - 测试不同用户权限下的按钮显示
   - 测试异常状态下的界面表现
