/**
 * 分机号管理模拟数据服务
 */
import { ExtensionItem, ExtensionFormData, ExtensionListParams } from './types';

const STORAGE_KEY = 'extension_mock_data';

// 初始模拟数据
const initialMockData: ExtensionItem[] = [
  {
    id: '1',
    sipAccount: '1001',
    sipPassword: '123456',
    deviceCode: 'DEV001',
    status: 0,
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00',
    remark: '测试分机号1',
  },
  {
    id: '2',
    sipAccount: '1002',
    sipPassword: '123456',
    deviceCode: 'DEV002',
    status: 1,
    createTime: '2024-01-16 14:20:00',
    updateTime: '2024-01-16 14:20:00',
    remark: '测试分机号2',
  },
  {
    id: '3',
    sipAccount: '1003',
    sipPassword: '123456',
    deviceCode: 'DEV003',
    status: 0,
    createTime: '2024-01-17 09:15:00',
    updateTime: '2024-01-17 09:15:00',
    remark: '测试分机号3',
  },
];

// 获取存储的数据
const getStoredData = (): ExtensionItem[] => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      return JSON.parse(stored);
    }
  } catch (error) {
    console.error('获取存储数据失败:', error);
  }
  return initialMockData;
};

// 保存数据到存储
const saveData = (data: ExtensionItem[]): void => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
  } catch (error) {
    console.error('保存数据失败:', error);
  }
};

// 生成新的ID
const generateId = (): string => {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9);
};

// 格式化时间
const formatTime = (): string => {
  const now = new Date();
  return now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  });
};

// 模拟延迟
const delay = (ms: number = 300): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// 模拟数据服务
export const mockExtensionService = {
  // 获取分机号列表
  async getList(params: ExtensionListParams = {}) {
    await delay();
    
    const { page = 1, perPage = 10, sipAccount, deviceCode } = params;
    let data = getStoredData();
    
    // 过滤数据
    if (sipAccount) {
      data = data.filter(item => item.sipAccount?.includes(sipAccount));
    }
    if (deviceCode) {
      data = data.filter(item => item.deviceCode?.includes(deviceCode));
    }
    
    // 分页
    const total = data.length;
    const start = (page - 1) * perPage;
    const end = start + perPage;
    const items = data.slice(start, end);
    
    return {
      status: 0,
      msg: 'success',
      data: {
        items,
        total,
      },
    };
  },

  // 获取分机号详情
  async getDetail(id: string) {
    await delay();
    
    const data = getStoredData();
    const item = data.find(item => item.id === id);
    
    if (item) {
      return {
        status: 0,
        msg: 'success',
        data: item,
      };
    } else {
      return {
        status: 1,
        msg: '分机号不存在',
        data: null,
      };
    }
  },

  // 添加分机号
  async add(formData: ExtensionFormData) {
    await delay();
    
    const data = getStoredData();
    
    // 检查SIP账号是否已存在
    const exists = data.some(item => item.sipAccount === formData.sipAccount);
    if (exists) {
      return {
        status: 1,
        msg: 'SIP账号已存在',
        data: null,
      };
    }
    
    const newItem: ExtensionItem = {
      id: generateId(),
      sipAccount: formData.sipAccount,
      sipPassword: formData.sipPassword,
      deviceCode: formData.deviceCode,
      status: formData.status || 0,
      createTime: formatTime(),
      updateTime: formatTime(),
      remark: formData.remark || '',
    };
    
    data.push(newItem);
    saveData(data);
    
    return {
      status: 0,
      msg: '添加成功',
      data: newItem,
    };
  },

  // 修改分机号
  async update(id: string, formData: ExtensionFormData) {
    await delay();
    
    const data = getStoredData();
    const index = data.findIndex(item => item.id === id);
    
    if (index === -1) {
      return {
        status: 1,
        msg: '分机号不存在',
        data: null,
      };
    }
    
    // 检查SIP账号是否被其他记录使用
    const exists = data.some(item => item.id !== id && item.sipAccount === formData.sipAccount);
    if (exists) {
      return {
        status: 1,
        msg: 'SIP账号已存在',
        data: null,
      };
    }
    
    const updatedItem: ExtensionItem = {
      ...data[index],
      sipAccount: formData.sipAccount,
      sipPassword: formData.sipPassword,
      deviceCode: formData.deviceCode,
      status: formData.status || 0,
      updateTime: formatTime(),
      remark: formData.remark || '',
    };
    
    data[index] = updatedItem;
    saveData(data);
    
    return {
      status: 0,
      msg: '修改成功',
      data: updatedItem,
    };
  },

  // 删除分机号
  async delete(id: string) {
    await delay();
    
    const data = getStoredData();
    const index = data.findIndex(item => item.id === id);
    
    if (index === -1) {
      return {
        status: 1,
        msg: '分机号不存在',
        data: null,
      };
    }
    
    data.splice(index, 1);
    saveData(data);
    
    return {
      status: 0,
      msg: '删除成功',
      data: null,
    };
  },
};
