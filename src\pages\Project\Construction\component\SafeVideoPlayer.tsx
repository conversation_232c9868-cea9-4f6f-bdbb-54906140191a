import React, { useRef, useEffect, useState, useCallback } from 'react';
import { Spin, Alert } from 'antd';

// 萤石云播放器类型定义
declare global {
  interface Window {
    EZUIKitPlayer: any;
  }
}

interface SafeVideoPlayerProps {
  ezOpenUrl?: string;
  onCapture?: (type: 'image' | 'video', data: any) => void;
}

/**
 * 安全的视频播放器组件
 * 使用iframe隔离来避免DOM冲突
 */
const SafeVideoPlayer: React.FC<SafeVideoPlayerProps> = ({
  ezOpenUrl,
  onCapture
}) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const playerIdRef = useRef<string>('');

  // 生成唯一的播放器ID
  const generatePlayerId = useCallback(() => {
    return `safe-video-player-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  // 创建iframe内容
  const createIframeContent = useCallback((url: string, playerId: string) => {
    return `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Video Player</title>
  <script src="https://open.ys7.com/sdk/js/1.4.8/ezuikit.js"></script>
  <style>
    body {
      margin: 0;
      padding: 0;
      background: #000;
      overflow: hidden;
    }
    #${playerId} {
      width: 100%;
      height: 100vh;
    }
    .loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: white;
      font-family: Arial, sans-serif;
    }
    .error {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #ff4d4f;
      font-family: Arial, sans-serif;
      text-align: center;
    }
  </style>
</head>
<body>
  <div id="${playerId}"></div>
  <div id="loading" class="loading">正在加载视频...</div>
  <div id="error" class="error" style="display: none;">暂无视频</div>
  
  <script>
    let player = null;
    
    // 获取访问令牌
    async function fetchAccessToken() {
      try {
        const response = await fetch('/api/ys/get_access_token', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        });
        
        if (!response.ok) {
          throw new Error('获取访问令牌失败');
        }
        
        const data = await response.json();
        return data.data;
      } catch (error) {
        console.error('获取访问令牌失败:', error);
        throw error;
      }
    }
    
    // 初始化播放器
    async function initPlayer() {
      try {
        const token = await fetchAccessToken();
        
        player = new EZUIKitPlayer({
          id: '${playerId}',
          accessToken: token,
          url: '${url}',
          template: 'pcRec',
          audio: true,
          handleSuccess: () => {
            console.log('播放器初始化成功');
            document.getElementById('loading').style.display = 'none';
            
            // 通知父窗口初始化成功
            window.parent.postMessage({
              type: 'player-success',
              playerId: '${playerId}'
            }, '*');
          },
          handleError: (err) => {
            console.error('播放器错误:', err);
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            
            // 通知父窗口初始化失败
            window.parent.postMessage({
              type: 'player-error',
              playerId: '${playerId}',
              error: err
            }, '*');
          }
        });
        
        // 监听抓拍事件
        if (player.eventEmitter) {
          player.eventEmitter.on('capturePicture', (eventData) => {
            window.parent.postMessage({
              type: 'capture-image',
              playerId: '${playerId}',
              data: eventData
            }, '*');
          });
          
          player.eventEmitter.on('stopSave', (eventData) => {
            window.parent.postMessage({
              type: 'capture-video',
              playerId: '${playerId}',
              data: eventData
            }, '*');
          });
        }
        
      } catch (error) {
        console.error('初始化播放器失败:', error);
        document.getElementById('loading').style.display = 'none';
        document.getElementById('error').style.display = 'block';
        
        window.parent.postMessage({
          type: 'player-error',
          playerId: '${playerId}',
          error: error.message
        }, '*');
      }
    }
    
    // 页面加载完成后初始化
    window.addEventListener('load', initPlayer);
    
    // 监听来自父窗口的消息
    window.addEventListener('message', (event) => {
      if (event.data.type === 'capture-image' && player) {
        // 触发抓拍
        if (player.capturePicture) {
          player.capturePicture();
        }
      } else if (event.data.type === 'start-record' && player) {
        // 开始录像
        if (player.startSave) {
          player.startSave();
        }
      } else if (event.data.type === 'stop-record' && player) {
        // 停止录像
        if (player.stopSave) {
          player.stopSave();
        }
      }
    });
  </script>
</body>
</html>
    `;
  }, []);

  // 处理来自iframe的消息
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data.playerId !== playerIdRef.current) return;

      switch (event.data.type) {
        case 'player-success':
          setLoading(false);
          setError('');
          break;

        case 'player-error':
          setLoading(false);
          setError('暂无视频');
          break;

        case 'capture-image':
          if (onCapture) {
            onCapture('image', event.data.data);
          }
          break;

        case 'capture-video':
          if (onCapture) {
            onCapture('video', event.data.data);
          }
          break;
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [onCapture]);

  // URL变化时重新加载iframe
  useEffect(() => {
    if (!ezOpenUrl) {
      setError('暂无视频');
      setLoading(false);
      return;
    }

    setLoading(true);
    setError('');

    const playerId = generatePlayerId();
    playerIdRef.current = playerId;

    if (iframeRef.current) {
      const iframeContent = createIframeContent(ezOpenUrl, playerId);

      // 使用srcdoc而不是blob URL，避免安全策略问题
      iframeRef.current.srcdoc = iframeContent;
    }
  }, [ezOpenUrl, createIframeContent, generatePlayerId]);

  // 抓拍方法
  const capturePicture = useCallback(() => {
    if (iframeRef.current) {
      iframeRef.current.contentWindow?.postMessage({
        type: 'capture-image'
      }, '*');
    }
  }, []);

  // 开始录像
  const startRecord = useCallback(() => {
    if (iframeRef.current) {
      iframeRef.current.contentWindow?.postMessage({
        type: 'start-record'
      }, '*');
    }
  }, []);

  // 停止录像
  const stopRecord = useCallback(() => {
    if (iframeRef.current) {
      iframeRef.current.contentWindow?.postMessage({
        type: 'stop-record'
      }, '*');
    }
  }, []);

  if (error) {
    return (
      <div style={{
        height: '400px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: '#000'
      }}>
        <Alert
          message={error}
          type="info"
          showIcon={false}
          style={{ background: 'transparent', border: 'none', color: '#fff' }}
        />
      </div>
    );
  }

  return (
    <div style={{ position: 'relative', height: '400px', background: '#000' }}>
      {loading && (
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          zIndex: 10
        }}>
          <Spin size="large" />
        </div>
      )}

      <iframe
        ref={iframeRef}
        style={{
          width: '100%',
          height: '100%',
          border: 'none',
          background: '#000'
        }}
        sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"
        title="Video Player"
        allow="camera; microphone; fullscreen"
      />
    </div>
  );
};

export default SafeVideoPlayer;
