import React, { useRef } from 'react';
import { Button, message, Modal, Tag } from 'antd';
import { ProTable, ActionType, ProColumns } from '@ant-design/pro-components';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { history } from '@umijs/max';
import { extensionService } from './mockService';
import { ExtensionItem } from './types';

// 在开发环境中引入测试文件
if (process.env.NODE_ENV === 'development') {
  import('./test');
}

const ExtensionManagement: React.FC = () => {
  const actionRef = useRef<ActionType>();

  // 删除分机号
  const handleDelete = async (record: ExtensionItem) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除账号 "${record.account}" 吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const result = await extensionService.delete(record.id);
          if (result.status === 0) {
            message.success('删除成功');
            actionRef.current?.reload();
          } else {
            message.error(result.msg || '删除失败');
          }
        } catch (error) {
          message.error('删除失败，请重试');
        }
      },
    });
  };

  // 表格列定义
  const columns: ProColumns<ExtensionItem>[] = [
    {
      title: '账号',
      dataIndex: 'account',
      key: 'account',
      width: 120,
    },
    {
      title: '密码',
      dataIndex: 'password',
      key: 'password',
      width: 120,
      search: false,
      render: () => '******', // 隐藏密码显示
    },
    {
      title: '关联Code',
      dataIndex: 'bindCode',
      key: 'bindCode',
      width: 150,
      search: false,
      render: (text) => text || '-',
    },
    {
      title: '绑定类型',
      dataIndex: 'bindType',
      key: 'bindType',
      width: 100,
      search: false,
      render: (_, record) => {
        if (record.bindType === 1) return <Tag color="blue">设备</Tag>;
        if (record.bindType === 2) return <Tag color="green">用户</Tag>;
        return '-';
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      search: false,
      render: (_, record) => {
        const isNormal = record.status === 0;
        return (
          <Tag color={isNormal ? 'green' : 'red'}>
            {isNormal ? '正常' : '禁用'}
          </Tag>
        );
      },
    },
    {
      title: '客户ID',
      dataIndex: 'corpId',
      key: 'corpId',
      width: 150,
      search: false,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      search: false,
      valueType: 'dateTime',
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 180,
      search: false,
      valueType: 'dateTime',
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      search: false,
      render: (_, record) => [
        <Button
          key="edit"
          type="link"
          size="small"
          icon={<EditOutlined />}
          onClick={() => {
            history.push(`/system/extension/edit?id=${record.id}`);
          }}
        >
          编辑
        </Button>,
        <Button
          key="delete"
          type="link"
          size="small"
          danger
          icon={<DeleteOutlined />}
          onClick={() => handleDelete(record)}
        >
          删除
        </Button>,
      ],
    },
  ];

  return (
    <ProTable<ExtensionItem>
      headerTitle="分机号管理"
      actionRef={actionRef}
      columns={columns}
      request={async (params) => {
        try {
          const { current, pageSize, account, ...rest } = params;
          const requestParams = {
            page: current,
            perPage: pageSize,
            ...(account && { account }),
            ...rest,
          };

          const result = await extensionService.getList(requestParams);

          if (result.status === 0 && result.data) {
            return {
              data: result.data.items || [],
              success: true,
              total: result.data.total || 0,
            };
          }
          return {
            data: [],
            success: false,
            total: 0,
          };
        } catch (error) {
          console.error('获取分机号列表失败:', error);
          message.error('获取数据失败');
          return {
            data: [],
            success: false,
            total: 0,
          };
        }
      }}
      rowKey="id"
      search={{
        defaultCollapsed: false,
        labelWidth: 'auto',
        span: 6,
      }}
      pagination={{
        defaultPageSize: 10,
        showSizeChanger: true,
        showQuickJumper: true,
      }}
      toolBarRender={() => [
        <Button
          key="add"
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            history.push('/system/extension/add');
          }}
        >
          添加分机号
        </Button>,
      ]}
    />
  );
};

export default ExtensionManagement;
