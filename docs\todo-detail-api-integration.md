# 待办详情API接口对接

## 🎯 接口信息

### API地址
```
GET /todo/get_info?id={todoId}
```

### 请求参数
- **id** (integer, 必需): 待办ID

### 请求示例
```
GET /todo/get_info?id=123
```

## 🔧 代码实现

### 1. 修改API调用
```typescript
// 获取待办详情数据
const fetchTodoDetail = async () => {
    if (!todoId) return;

    setLoading(true);
    try {
        console.log('开始获取待办详情，todoId:', todoId);
        
        // 调用真实的API接口
        const response = await getRequest(`/todo/get_info?id=${todoId}`);
        console.log('待办详情API响应:', response);
        
        // 打印完整的响应数据供调试
        console.log('=== 待办详情完整数据 ===');
        console.log('响应状态:', response.status);
        console.log('响应消息:', response.msg);
        console.log('响应数据:', JSON.stringify(response.data, null, 2));
        console.log('========================');
        
        if (response.status === 0 && response.data) {
            // 暂时使用模拟数据结构，等看到真实数据后再调整
            const mockData: TodoDetailData = {
                // ... 模拟数据
            };
            setTodoData(mockData);
            
            // 如果有设备code，获取直播地址
            if (mockData.deviceCode) {
                fetchLiveAddress(mockData.deviceCode);
            }
        } else {
            console.error('API返回错误:', response.msg);
            message.error(response.msg || '获取待办详情失败');
        }
    } catch (error) {
        console.error('获取待办详情出错:', error);
        message.error('获取待办详情失败');
    } finally {
        setLoading(false);
    }
};
```

### 2. 调试日志输出
代码中添加了详细的调试日志，包括：
- 请求开始日志
- API响应日志
- 格式化的数据打印
- 错误处理日志

## 🧪 测试步骤

### 1. 打开待办详情页面
1. 进入待办列表页面
2. 点击任意一个待办项目
3. 打开待办详情侧边栏

### 2. 查看控制台日志
打开浏览器开发者工具的Console标签页，应该能看到：

```
开始获取待办详情，todoId: 123
待办详情API响应: {status: 0, msg: "success", data: {...}}
=== 待办详情完整数据 ===
响应状态: 0
响应消息: success
响应数据: {
  "id": 123,
  "title": "...",
  "deviceCode": "...",
  // ... 其他字段
}
========================
```

### 3. 数据分析
根据控制台输出的数据，我们需要分析：
1. **数据结构**: 查看response.data的完整结构
2. **字段映射**: 确定哪些字段对应我们需要的数据
3. **设备信息**: 查找deviceCode或类似的设备标识字段
4. **钻孔信息**: 查找钻孔相关的参数数据

## 📊 预期数据结构

根据待办详情的功能需求，我们期望API返回的数据包含：

### 基本信息
- id: 待办ID
- title: 标题
- drillName: 钻机名称
- deviceCode: 设备代码（用于获取直播地址）
- holeNumber: 孔号

### 钻孔参数
- planHoleAngle: 计划开孔角度
- actualHoleAngle: 实际开孔角度
- planAzimuth: 计划开孔方位角
- actualAzimuth: 实际开孔方位角
- planHeight: 计划开孔高度
- actualHeight: 实际开孔高度
- planCoalDistance: 计划见煤距离
- actualCoalDistance: 实际见煤距离
- planRockDistance: 计划见岩距离
- actualRockDistance: 实际见岩距离
- planDepth: 计划孔深
- actualDepth: 实际孔深

### 时间信息
- createTime: 创建时间
- updateTime: 修改时间

### 媒体文件
- images: 图片列表
- videos: 视频列表

### 审批流程
- approvalSteps: 审批流程节点
- status: 当前状态

## 🔄 下一步计划

1. **查看真实数据**: 通过控制台日志查看API返回的真实数据结构
2. **字段映射**: 根据真实数据调整TodoDetailData接口定义
3. **数据转换**: 实现从API数据到组件数据的转换逻辑
4. **设备关联**: 确认deviceCode字段，用于获取直播地址
5. **测试验证**: 确保所有功能正常工作

## 🚨 注意事项

### 1. 数据格式
- 确保API返回的数字类型字段能正确解析
- 注意时间格式的处理
- 检查数组字段是否为空的处理

### 2. 错误处理
- API返回错误状态的处理
- 网络请求失败的处理
- 数据格式异常的处理

### 3. 设备代码
- 确认deviceCode字段的存在和格式
- 如果没有deviceCode，需要确认如何获取设备信息
- 可能需要通过其他字段（如drillName）来关联设备

## 📁 相关文件

- `src/pages/Todo/components/TodoDetail.tsx` - 主要修改文件
- `docs/todo-detail-api-integration.md` - 本文档

## 🎯 测试结果

请在控制台查看输出的数据，然后提供给我，我将根据真实的数据结构来调整代码实现。

预期看到的关键信息：
1. API响应的完整数据结构
2. 设备相关字段（deviceCode或类似字段）
3. 钻孔参数字段
4. 审批流程相关字段
5. 媒体文件相关字段

有了这些信息后，我们就可以完善数据映射和组件渲染逻辑了！🚀
