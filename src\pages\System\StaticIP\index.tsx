import React, { useRef } from 'react';
import { Button, message, Modal, Tag } from 'antd';
import { ProTable, ActionType, ProColumns } from '@ant-design/pro-components';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { history } from '@umijs/max';
import { staticIPService } from './service';
import { StaticIPItem } from './types';

const StaticIPManagement: React.FC = () => {
  const actionRef = useRef<ActionType>();

  // 删除静态IP
  const handleDelete = async (record: StaticIPItem) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除IP地址 "${record.ipAddress}" 吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const result = await staticIPService.delete(record.id);
          if (result.status === 0) {
            message.success('删除成功');
            actionRef.current?.reload();
          } else {
            message.error(result.msg || '删除失败');
          }
        } catch (error) {
          message.error('删除失败，请重试');
        }
      },
    });
  };

  // 跳转到编辑页面
  const handleEdit = (record: StaticIPItem) => {
    history.push(`/system/staticip/edit?id=${record.id}`);
  };

  // 跳转到添加页面
  const handleAdd = () => {
    history.push('/system/staticip/add');
  };

  // 表格列定义
  const columns: ProColumns<StaticIPItem>[] = [
    {
      title: 'IP地址',
      dataIndex: 'ipAddress',
      key: 'ipAddress',
      width: 150,
      copyable: true,
    },
    {
      title: '设备编码',
      dataIndex: 'deviceCode',
      key: 'deviceCode',
      width: 200,
      copyable: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      search: false,
      render: (_, record) => {
        const isNormal = record.status === 0;
        return (
          <Tag color={isNormal ? 'green' : 'red'}>
            {isNormal ? '正常' : '禁用'}
          </Tag>
        );
      },
    },
    {
      title: '客户ID',
      dataIndex: 'corpId',
      key: 'corpId',
      width: 150,
      search: false,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      search: false,
      valueType: 'dateTime',
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 180,
      search: false,
      valueType: 'dateTime',
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      search: false,
      render: (_, record) => [
        <Button
          key="edit"
          type="link"
          size="small"
          icon={<EditOutlined />}
          onClick={() => handleEdit(record)}
        >
          编辑
        </Button>,
        <Button
          key="delete"
          type="link"
          size="small"
          danger
          icon={<DeleteOutlined />}
          onClick={() => handleDelete(record)}
        >
          删除
        </Button>,
      ],
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <ProTable<StaticIPItem>
        headerTitle="静态IP列表"
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 'auto',
        }}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            添加静态IP
          </Button>,
        ]}
        columns={columns}
        request={async (params) => {
          try {
            const { current, pageSize, ipAddress, deviceCode, ...rest } = params;
            const requestParams = {
              page: current,
              perPage: pageSize,
              ...(ipAddress && { ipAddress }),
              ...(deviceCode && { deviceCode }),
              ...rest,
            };

            const result = await staticIPService.getList(requestParams);

            if (result.status === 0 && result.data) {
              return {
                data: result.data.items || [],
                success: true,
                total: result.data.total || 0,
              };
            }
            return {
              data: [],
              success: false,
              total: 0,
            };
          } catch (error) {
            console.error('获取静态IP列表失败:', error);
            message.error('获取数据失败');
            return {
              data: [],
              success: false,
              total: 0,
            };
          }
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
      />
    </div>
  );
};

export default StaticIPManagement;
