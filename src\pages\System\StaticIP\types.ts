/**
 * 静态IP管理相关类型定义
 */

// 静态IP数据项
export interface StaticIPItem {
  id: number;                    // ID
  ipAddress: string;             // IP地址
  deviceCode: string;            // 设备编码
  status: number;                // 状态：0-正常 1-禁用
  corpId: string;                // 客户id
  createdAt: string;             // 创建时间
  updatedAt: string;             // 更新时间
}

// 静态IP表单数据
export interface StaticIPFormData {
  ipAddress: string;             // IP地址
  deviceCode: string;            // 设备编码
  status?: number;               // 状态：0-正常 1-禁用
}

// 静态IP列表查询参数
export interface StaticIPListParams {
  page?: number;                 // 页码
  perPage?: number;              // 每页数量
  ipAddress?: string;            // IP地址模糊查询
  deviceCode?: string;           // 设备编码模糊查询
}

// 静态IP列表响应数据
export interface StaticIPListResponse {
  items: StaticIPItem[];         // 数据列表
  total: number;                 // 总数
  page: number;                  // 当前页码
}

// API响应格式
export interface ApiResponse<T> {
  status: number;                // 状态码：0-成功 其他-失败
  msg: string;                   // 消息
  data: T;                       // 数据
}
