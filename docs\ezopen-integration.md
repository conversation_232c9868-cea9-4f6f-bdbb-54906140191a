# 萤石云 ezopen:// 协议集成说明

## 概述

本项目已成功集成萤石云 `ezuikit-js` SDK，支持 `ezopen://` 协议的视频播放功能。该功能主要用于施工记录详情页面的视频回放模块。

## 技术实现

### 1. 依赖安装

```bash
pnpm add ezuikit-js
```

### 2. 核心组件

#### EzVideoPlayer 组件
- **位置**: `src/pages/Project/Construction/component/EzVideoPlayer.tsx`
- **功能**: 萤石云视频播放器封装组件
- **特性**:
  - 自动获取 AccessToken
  - 错误处理和重试机制
  - 播放器生命周期管理
  - 支持回放模板

#### Video 组件增强
- **位置**: `src/pages/Project/Construction/component/video.tsx`
- **功能**: 原有视频播放组件，新增萤石云播放支持
- **特性**:
  - 双播放模式：标准播放器 + 萤石云播放器
  - 自动协议检测
  - 播放模式切换

### 3. API 集成

#### AccessToken 获取
- **接口**: `/api/ys/get_access_token`
- **返回格式**:
```json
{
  "status": 0,
  "msg": "request:ok",
  "data": "at.5ox4nj3c6md9en497btbgvhb55tkajko-5c19524f1a-0lqo60f-jitgbotkp"
}
```

#### 视频数据获取
- **接口**: `gbs/get_record`
- **支持协议**:
  - 标准视频 URL (http/https)
  - ezopen:// 协议 URL

## 使用方式

### 1. 施工记录详情页面

1. 访问施工记录详情页面
2. 点击"视频回放"标签页
3. 系统自动检测视频协议类型：
   - 如果是 `ezopen://` 协议，自动切换到萤石云播放器
   - 如果是标准协议，使用原有播放器
4. 用户可手动切换播放模式

### 2. ezopen URL 格式

```
ezopen://open.ys7.com/{设备序列号}:{通道号}/{类型}.{格式}?begin={开始时间}&end={结束时间}
```

**示例**:
```
ezopen://open.ys7.com/33010175992677797274:33010042991117112440/1.rec?begin=20250701000000&end=20250702235959
```

**参数说明**:
- `设备序列号`: 萤石云设备的唯一标识
- `通道号`: 设备通道号
- `类型`: 1=主通道, 2=子通道
- `格式`: live=直播, rec=回放, cloud.rec=云存储回放
- `begin/end`: 回放时间范围 (格式: yyyyMMddHHmmss)

## 配置说明

### 1. 萤石云播放器配置

```typescript
const player = new EZUIKitPlayer({
  id: containerId,           // 容器ID
  accessToken: token,        // 访问令牌
  url: ezOpenUrl,           // ezopen协议URL
  width: 800,               // 播放器宽度
  height: 450,              // 播放器高度
  template: 'pcRec',        // 回放模板
  audio: true,              // 启用音频
  handleSuccess: () => {},  // 成功回调
  handleError: (err) => {}  // 错误回调
});
```

### 2. 错误处理

常见错误码：
- `5`: 设备密码错误
- 网络错误: 自动重试
- 媒体错误: 降级处理

## 测试组件

### EzVideoTest 组件
- **位置**: `src/pages/Project/Construction/component/EzVideoTest.tsx`
- **用途**: 独立测试萤石云播放功能
- **功能**:
  - 手动输入 ezopen URL
  - 播放控制
  - 错误信息显示

## 注意事项

### 1. 网络要求
- 需要访问萤石云服务器
- 确保防火墙允许相关端口

### 2. 权限要求
- 需要有效的萤石云 AccessToken
- 设备必须在线且有访问权限

### 3. 浏览器兼容性
- 支持现代浏览器 (Chrome, Firefox, Safari, Edge)
- 需要支持 WebAssembly 和 SharedArrayBuffer

### 4. 性能优化
- 播放器会自动管理资源
- 组件卸载时自动清理播放器实例
- 支持多线程解码 (需要浏览器支持)

## 故障排除

### 1. 播放失败
- 检查 AccessToken 是否有效
- 确认设备在线状态
- 验证 ezopen URL 格式

### 2. 加载缓慢
- 检查网络连接
- 考虑使用本地解码库

### 3. 权限错误
- 确认设备访问权限
- 检查 AccessToken 权限范围

## 更新日志

- **v1.0.0**: 初始集成萤石云 ezopen 协议支持
- 支持自动协议检测
- 支持播放模式切换
- 完整的错误处理机制
